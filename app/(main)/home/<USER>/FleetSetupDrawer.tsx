"use client";

import { useState } from "react";
import { Building, Users, CheckCircle, ChevronRight } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface FleetSetupDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (hasExistingCompany: boolean) => void;
}

export default function FleetSetupDrawer({
  isOpen,
  onClose,
  onNext,
}: FleetSetupDrawerProps) {
  const [selectedOption, setSelectedOption] = useState<
    "existing" | "poolly" | null
  >(null);

  const handleContinue = () => {
    if (selectedOption) {
      onNext(selectedOption === "existing");
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ChevronRight size={24} className="rotate-180" />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Fleet Setup
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Choose how you want to set up your fleet management
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Introduction */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Company Structure
                </h4>
                <p className="text-sm text-[#797879] mb-4">
                  Do you have an existing registered company for your fleet
                  operations?
                </p>
              </div>

              {/* Options */}
              <div className="space-y-4">
                {/* Existing Company Option */}
                <button
                  onClick={() => setSelectedOption("existing")}
                  className={`w-full p-4 rounded-xl border-2 transition-all ${
                    selectedOption === "existing"
                      ? "border-[#009639] bg-[#e6ffe6]"
                      : "border-gray-200 bg-white hover:border-[#009639] hover:bg-[#f8fff8]"
                  }`}
                >
                  <div className="flex items-start">
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1 ${
                        selectedOption === "existing"
                          ? "bg-[#009639]"
                          : "bg-gray-200"
                      }`}
                    >
                      {selectedOption === "existing" ? (
                        <CheckCircle size={16} className="text-white" />
                      ) : (
                        <Building size={16} className="text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <h5 className="font-semibold text-[#333333] mb-1">
                        Yes, I have an existing company
                      </h5>
                      <p className="text-xs text-[#797879] leading-relaxed">
                        Register your existing business entity (Pty Ltd, Inc,
                        LLC, etc.) to manage your fleet operations
                      </p>
                      <div className="mt-2 text-xs text-[#009639] font-medium">
                        • Use existing registration details • Full business
                        control • Custom ownership structure
                      </div>
                    </div>
                  </div>
                </button>

                {/* Poolly Managed Option */}
                <button
                  onClick={() => setSelectedOption("poolly")}
                  className={`w-full p-4 rounded-xl border-2 transition-all ${
                    selectedOption === "poolly"
                      ? "border-[#009639] bg-[#e6ffe6]"
                      : "border-gray-200 bg-white hover:border-[#009639] hover:bg-[#f8fff8]"
                  }`}
                >
                  <div className="flex items-start">
                    <div
                      className={`w-6 h-6 rounded-full flex items-center justify-center mr-3 mt-1 ${
                        selectedOption === "poolly"
                          ? "bg-[#009639]"
                          : "bg-gray-200"
                      }`}
                    >
                      {selectedOption === "poolly" ? (
                        <CheckCircle size={16} className="text-white" />
                      ) : (
                        <Users size={16} className="text-gray-600" />
                      )}
                    </div>
                    <div className="flex-1 text-left">
                      <h5 className="font-semibold text-[#333333] mb-1">
                        No, use Poolly managed group
                      </h5>
                      <p className="text-xs text-[#797879] leading-relaxed">
                        Let Poolly handle the administrative setup while you
                        focus on your fleet operations
                      </p>
                      <div className="mt-2 text-xs text-[#009639] font-medium">
                        • Quick setup process • Poolly handles compliance •
                        Simplified management
                      </div>
                    </div>
                  </div>
                </button>
              </div>

              {/* Benefits Section */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-semibold text-green-900 mb-2 flex items-center">
                  <Building size={16} className="mr-2" />
                  Fleet Management Benefits
                </h5>
                <div className="space-y-2 text-sm text-green-800">
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Digital vehicle handovers</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Smart maintenance tracking</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Live GPS vehicle tracking</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle size={12} className="mr-2 text-green-600" />
                    <span>Secure document management</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleContinue}
              disabled={!selectedOption}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Continue
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onClose}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
