"use client";

import { useState } from "react";
import {
  ArrowLeft,
  DollarSign,
  Calendar,
  FileText,
  Users,
  CheckCircle,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface VehicleData {
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  images: File[];
  documents: any[];
}

interface FinancialTerms {
  weeklyRate: string;
  initiationFee: string;
  depositAmount: string;
  ownershipTimeline: string;
}

interface LesseeRequirements {
  experienceRequired: boolean;
  experienceQuestions: string[];
  requiredDocuments: string[];
}

interface ListingTermsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (terms: FinancialTerms, requirements: LesseeRequirements) => void;
  vehicleData: VehicleData | null;
}

export default function ListingTermsDrawer({
  isOpen,
  onClose,
  onSubmit,
  vehicleData,
}: ListingTermsDrawerProps) {
  const [currentStep, setCurrentStep] = useState<"financial" | "requirements">(
    "financial"
  );

  const [financialTerms, setFinancialTerms] = useState<FinancialTerms>({
    weeklyRate: "",
    initiationFee: "",
    depositAmount: "",
    ownershipTimeline: "36",
  });

  const [lesseeRequirements, setLesseeRequirements] =
    useState<LesseeRequirements>({
      experienceRequired: false,
      experienceQuestions: [],
      requiredDocuments: ["ID Document", "Driver's License"],
    });

  const availableDocuments = [
    "ID Document",
    "Driver's License",
    "Proof of Address",
    "Bank Statement - 3 Months",
    "PrDP (Professional Driving Permit)",
    "Operator Card",
  ];

  const experienceQuestions = [
    "Which e-hailing company do you currently drive for?",
    "How long have you been driving for e-hailing?",
    "What are your profile numbers/ratings?",
  ];

  const handleFinancialChange = (
    field: keyof FinancialTerms,
    value: string
  ) => {
    setFinancialTerms((prev) => ({ ...prev, [field]: value }));
  };

  const handleDocumentToggle = (document: string) => {
    setLesseeRequirements((prev) => ({
      ...prev,
      requiredDocuments: prev.requiredDocuments.includes(document)
        ? prev.requiredDocuments.filter((doc) => doc !== document)
        : [...prev.requiredDocuments, document],
    }));
  };

  const handleExperienceToggle = () => {
    setLesseeRequirements((prev) => ({
      ...prev,
      experienceRequired: !prev.experienceRequired,
      experienceQuestions: !prev.experienceRequired ? experienceQuestions : [],
    }));
  };

  const canProceedFromFinancial = () => {
    return (
      financialTerms.weeklyRate &&
      financialTerms.initiationFee &&
      financialTerms.depositAmount &&
      financialTerms.ownershipTimeline
    );
  };

  const canSubmit = () => {
    return lesseeRequirements.requiredDocuments.length >= 2; // At least ID and License
  };

  const handleNext = () => {
    if (currentStep === "financial" && canProceedFromFinancial()) {
      setCurrentStep("requirements");
    } else if (currentStep === "requirements" && canSubmit()) {
      onSubmit(financialTerms, lesseeRequirements);
    }
  };

  const handleBack = () => {
    if (currentStep === "requirements") {
      setCurrentStep("financial");
    } else {
      onClose();
    }
  };

  // Return early if no vehicle data
  if (!vehicleData) {
    return null;
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  {currentStep === "financial"
                    ? "Financial Terms"
                    : "Lessee Requirements"}
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {currentStep === "financial"
                    ? "Set your rates and terms"
                    : "Define requirements for potential lessees"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span
                className={
                  currentStep === "financial"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Financial Terms
              </span>
              <span
                className={
                  currentStep === "requirements"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Requirements
              </span>
            </div>
            <div className="mt-2 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{ width: currentStep === "financial" ? "50%" : "100%" }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {/* Vehicle Summary */}
            <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4 mb-6">
              <h4 className="font-semibold text-[#333333] mb-2">
                Your Vehicle
              </h4>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-[#333333]">
                    {vehicleData.make} {vehicleData.model}
                  </p>
                  <p className="text-xs text-[#797879]">
                    {vehicleData.year} • {vehicleData.color} •{" "}
                    {Number(vehicleData.mileage).toLocaleString()}km
                  </p>
                </div>
                <span className="text-xs bg-[#e6ffe6] text-[#009639] px-2 py-1 rounded-full font-medium">
                  {vehicleData.condition}
                </span>
              </div>
            </div>

            {currentStep === "financial" && (
              <div className="space-y-6">
                {/* Pricing & Terms */}
                <div>
                  <div className="flex items-center mb-3">
                    <DollarSign size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Pricing & Terms
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Weekly Rate (R)
                        </label>
                        <input
                          type="number"
                          value={financialTerms.weeklyRate}
                          onChange={(e) =>
                            handleFinancialChange("weeklyRate", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 2700"
                        />
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Initiation Fee (R)
                        </label>
                        <input
                          type="number"
                          value={financialTerms.initiationFee}
                          onChange={(e) =>
                            handleFinancialChange(
                              "initiationFee",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 7500"
                        />
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Security Deposit (R)
                        </label>
                        <input
                          type="number"
                          value={financialTerms.depositAmount}
                          onChange={(e) =>
                            handleFinancialChange(
                              "depositAmount",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. 5000"
                        />
                      </div>

                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Path to Ownership (months)
                        </label>
                        <select
                          value={financialTerms.ownershipTimeline}
                          onChange={(e) =>
                            handleFinancialChange(
                              "ownershipTimeline",
                              e.target.value
                            )
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        >
                          <option value="24">24 months</option>
                          <option value="36">36 months</option>
                          <option value="48">48 months</option>
                          <option value="60">60 months</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <p className="text-xs text-blue-800">
                    <strong>Tip:</strong> Competitive rates attract more
                    qualified lessees. Consider market rates and your vehicle's
                    condition when setting prices.
                  </p>
                </div>

                {/* What Happens Next */}
                <div>
                  <div className="flex items-center mb-3">
                    <Calendar size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      What Happens Next
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-3">
                      {[
                        "Submit listing for review",
                        "Document verification",
                        "Vehicle inspection scheduled",
                        "Listing goes live",
                        "Receive lessee applications",
                        "Select qualified lessees",
                      ].map((step, index) => (
                        <div
                          key={index}
                          className="flex items-center text-sm text-[#797879]"
                        >
                          <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                            {index + 1}
                          </div>
                          {step}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "requirements" && (
              <div className="space-y-6">
                {/* Experience Requirements */}
                <div>
                  <div className="flex items-center mb-3">
                    <Users size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Experience Requirements
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-[#333333]">
                            Require E-hailing Experience
                          </p>
                          <p className="text-xs text-[#797879]">
                            Ask lessees about their driving experience
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            className="sr-only peer"
                            checked={lesseeRequirements.experienceRequired}
                            onChange={handleExperienceToggle}
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-[#009639]"></div>
                        </label>
                      </div>

                      {lesseeRequirements.experienceRequired && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <p className="text-xs text-green-800 mb-2">
                            <strong>
                              Experience questions that will be asked:
                            </strong>
                          </p>
                          <ul className="text-xs text-green-700 space-y-1">
                            {experienceQuestions.map((question, index) => (
                              <li key={index}>• {question}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Required Documents */}
                <div>
                  <div className="flex items-center mb-3">
                    <FileText size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Required Documents
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <p className="text-sm text-[#797879] mb-4">
                      Select which documents lessees must provide
                    </p>

                    <div className="space-y-2">
                      {availableDocuments.map((document) => (
                        <div
                          key={document}
                          className="flex items-center justify-between p-2 border border-gray-200 rounded-lg"
                        >
                          <span className="text-sm text-[#333333]">
                            {document}
                          </span>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              className="sr-only peer"
                              checked={lesseeRequirements.requiredDocuments.includes(
                                document
                              )}
                              onChange={() => handleDocumentToggle(document)}
                              disabled={
                                document === "ID Document" ||
                                document === "Driver's License"
                              }
                            />
                            <div
                              className={`w-5 h-5 border-2 rounded flex items-center justify-center ${
                                lesseeRequirements.requiredDocuments.includes(
                                  document
                                )
                                  ? "bg-[#009639] border-[#009639]"
                                  : "border-gray-300"
                              } ${document === "ID Document" || document === "Driver's License" ? "opacity-50" : ""}`}
                            >
                              {lesseeRequirements.requiredDocuments.includes(
                                document
                              ) && (
                                <CheckCircle size={12} className="text-white" />
                              )}
                            </div>
                          </label>
                        </div>
                      ))}
                    </div>

                    <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                      <p className="text-xs text-yellow-800">
                        <strong>Note:</strong> ID Document and Driver's License
                        are mandatory and cannot be removed. Additional
                        documents help verify lessee reliability.
                      </p>
                    </div>
                  </div>
                </div>

                {/* Lessee Matching Process */}
                <div>
                  <div className="flex items-center mb-3">
                    <Users size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Lessee Matching Process
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-3">
                      {[
                        "Qualified drivers apply",
                        "Review applications & documents",
                        "Interview potential lessees",
                        "Select preferred lessee",
                        "Sign lease agreement",
                        "Vehicle handover & start earning",
                      ].map((step, index) => (
                        <div
                          key={index}
                          className="flex items-center text-sm text-[#797879]"
                        >
                          <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                            {index + 1}
                          </div>
                          {step}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "financial" && !canProceedFromFinancial()) ||
                (currentStep === "requirements" && !canSubmit())
              }
              className={`w-full py-3 rounded-full font-semibold transition-all ${
                (currentStep === "financial" && canProceedFromFinancial()) ||
                (currentStep === "requirements" && canSubmit())
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              {currentStep === "financial"
                ? "Continue to Requirements"
                : "Submit for Approval"}
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
