"use client";

import { useState } from "react";
import { ArrowLeft, Upload, FileText, CheckCircle } from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDes<PERSON>,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Vehicle {
  id: number;
  make: string;
  model: string;
  year: number;
  weeklyRate: number;
  requirements: {
    minDeposit: number;
    documents: string[];
  };
}

interface ApplicationProcessDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  selectedVehicle: Vehicle | null;
}

interface DocumentUpload {
  name: string;
  uploaded: boolean;
  required: boolean;
  file?: File;
}

interface ExperienceData {
  hasExperience: boolean;
  duration?: string;
  company?: string;
  reasonStopped?: string;
  workType?: "part-time" | "full-time";
  profileNumber?: string;
}

export default function ApplicationProcessDrawer({
  isOpen,
  onClose,
  onSubmit,
  selectedVehicle,
}: ApplicationProcessDrawerProps) {
  const [documents, setDocuments] = useState<DocumentUpload[]>([
    { name: "ID Document", uploaded: false, required: true },
    { name: "Driver's license", uploaded: false, required: true },
    { name: "Bank Statement - 3 months", uploaded: false, required: false },
    { name: "Proof of residence", uploaded: false, required: false },
    {
      name: "PrDP (Professional driving permit)",
      uploaded: false,
      required: false,
    },
    {
      name: "Operator card or public operating licence",
      uploaded: false,
      required: false,
    },
  ]);

  const [experience, setExperience] = useState<ExperienceData>({
    hasExperience: false,
  });

  const [currentStep, setCurrentStep] = useState<"documents" | "experience">(
    "documents"
  );

  const handleDocumentUpload = (index: number, file: File) => {
    const updatedDocuments = [...documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      uploaded: true,
      file,
    };
    setDocuments(updatedDocuments);
  };

  const handleExperienceChange = (field: keyof ExperienceData, value: any) => {
    setExperience((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const canProceedToExperience = documents
    .filter((doc) => doc.required)
    .every((doc) => doc.uploaded);
  const canSubmitApplication =
    canProceedToExperience &&
    (experience.hasExperience
      ? experience.duration &&
        experience.company &&
        experience.workType &&
        experience.profileNumber
      : true);

  const handleNext = () => {
    if (currentStep === "documents" && canProceedToExperience) {
      setCurrentStep("experience");
    } else if (currentStep === "experience" && canSubmitApplication) {
      onSubmit();
    }
  };

  const handleBack = () => {
    if (currentStep === "experience") {
      setCurrentStep("documents");
    } else {
      onClose();
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application Process
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {currentStep === "documents"
                    ? "Upload your required documents"
                    : "Tell us about your e-hailing experience"}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span
                className={
                  currentStep === "documents"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Documents
              </span>
              <span
                className={
                  currentStep === "experience"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Experience
              </span>
            </div>
            <div className="mt-2 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{
                  width: currentStep === "documents" ? "50%" : "100%",
                }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-8">
              {/* Vehicle Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <h4 className="font-semibold text-[#333333] mb-2">
                  Selected Vehicle
                </h4>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-[#333333]">
                      {selectedVehicle?.make} {selectedVehicle?.model}
                    </p>
                    <p className="text-xs text-[#797879]">
                      {selectedVehicle?.year} Model
                    </p>
                  </div>
                  <span className="text-sm font-bold text-[#009639]">
                    R{selectedVehicle?.weeklyRate.toLocaleString()}/week
                  </span>
                </div>
              </div>

              {/* Step Indicator */}
              <div className="flex items-center justify-center space-x-8">
                <div
                  className={`flex items-center ${currentStep === "documents" ? "text-[#009639]" : "text-gray-400"}`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                      currentStep === "documents"
                        ? "bg-[#009639] text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    1
                  </div>
                  <span className="ml-2 text-sm font-medium">Documents</span>
                </div>
                <div className="w-8 h-px bg-gray-300"></div>
                <div
                  className={`flex items-center ${currentStep === "experience" ? "text-[#009639]" : "text-gray-400"}`}
                >
                  <div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                      currentStep === "experience"
                        ? "bg-[#009639] text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    2
                  </div>
                  <span className="ml-2 text-sm font-medium">Experience</span>
                </div>
              </div>

              {/* Documents Step */}
              {currentStep === "documents" && (
                <div className="space-y-6">
                  <div>
                    <div className="flex items-center mb-3">
                      <FileText size={18} className="mr-2 text-[#009639]" />
                      <h4 className="font-semibold text-[#333333]">
                        Upload Documents
                      </h4>
                    </div>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-6">
                        {documents.map((doc, index) => (
                          <div key={index} className=" rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center">
                                <span className="text-sm font-medium text-[#333333]">
                                  {doc.name}
                                </span>
                                {doc.required ? (
                                  <span className="ml-2 text-xs bg-red-100 text-red-600 px-2 py-1 rounded-full">
                                    Required
                                  </span>
                                ) : (
                                  <span className="ml-2 text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                                    Optional
                                  </span>
                                )}
                              </div>
                              {doc.uploaded && (
                                <CheckCircle
                                  size={16}
                                  className="text-green-500"
                                />
                              )}
                            </div>
                            {!doc.uploaded ? (
                              <label className="flex items-center justify-center w-full h-20 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] transition-colors">
                                <div className="text-center">
                                  <Upload
                                    size={20}
                                    className="mx-auto mb-1 text-gray-400"
                                  />
                                  <span className="text-xs text-gray-500">
                                    Click to upload
                                  </span>
                                </div>
                                <input
                                  type="file"
                                  className="hidden"
                                  accept=".pdf,.jpg,.jpeg,.png"
                                  onChange={(e) => {
                                    const file = e.target.files?.[0];
                                    if (file) {
                                      handleDocumentUpload(index, file);
                                    }
                                  }}
                                />
                              </label>
                            ) : (
                              <div className="flex items-center justify-between bg-green-50 border border-green-200 rounded-lg p-2">
                                <span className="text-xs text-green-700">
                                  {doc.file?.name || "Document uploaded"}
                                </span>
                                <button
                                  onClick={() => {
                                    const updatedDocuments = [...documents];
                                    updatedDocuments[index] = {
                                      ...updatedDocuments[index],
                                      uploaded: false,
                                      file: undefined,
                                    };
                                    setDocuments(updatedDocuments);
                                  }}
                                  className="text-xs text-red-600 hover:text-red-800"
                                >
                                  Remove
                                </button>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Experience Step */}
              {currentStep === "experience" && (
                <div className="space-y-6">
                  <div>
                    <h4 className="font-semibold text-[#333333] mb-3">
                      E-hailing Experience
                    </h4>
                    <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                      <div className="space-y-4">
                        {/* Has Experience Question */}
                        <div>
                          <label className="text-sm font-medium text-[#333333] mb-2 block">
                            Have you driven for e-hailing before or currently?
                          </label>
                          <div className="flex space-x-4">
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === true}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", true)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">
                                Yes
                              </span>
                            </label>
                            <label className="flex items-center">
                              <input
                                type="radio"
                                name="hasExperience"
                                checked={experience.hasExperience === false}
                                onChange={() =>
                                  handleExperienceChange("hasExperience", false)
                                }
                                className="mr-2"
                              />
                              <span className="text-sm text-[#333333]">No</span>
                            </label>
                          </div>
                        </div>

                        {/* Experience Details - Show only if has experience */}
                        {experience.hasExperience && (
                          <div className="space-y-4 pt-4 border-t border-gray-200">
                            {/* Duration */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                How long have you been driving?
                              </label>
                              <select
                                value={experience.duration || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "duration",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select duration</option>
                                <option value="less-than-6-months">
                                  Less than 6 months
                                </option>
                                <option value="6-months-1-year">
                                  6 months - 1 year
                                </option>
                                <option value="1-2-years">1 - 2 years</option>
                                <option value="2-5-years">2 - 5 years</option>
                                <option value="more-than-5-years">
                                  More than 5 years
                                </option>
                              </select>
                            </div>

                            {/* Company */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Which company?
                              </label>
                              <select
                                value={experience.company || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "company",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              >
                                <option value="">Select company</option>
                                <option value="uber">Uber</option>
                                <option value="bolt">Bolt</option>
                                <option value="indriver">InDriver</option>
                                <option value="other">Other</option>
                              </select>
                            </div>

                            {/* Work Type */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-2 block">
                                Part time or full time basis?
                              </label>
                              <div className="flex space-x-4">
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "part-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "part-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Part-time
                                  </span>
                                </label>
                                <label className="flex items-center">
                                  <input
                                    type="radio"
                                    name="workType"
                                    checked={
                                      experience.workType === "full-time"
                                    }
                                    onChange={() =>
                                      handleExperienceChange(
                                        "workType",
                                        "full-time"
                                      )
                                    }
                                    className="mr-2"
                                  />
                                  <span className="text-sm text-[#333333]">
                                    Full-time
                                  </span>
                                </label>
                              </div>
                            </div>

                            {/* Profile Number */}
                            <div>
                              <label className="text-sm font-medium text-[#333333] mb-1 block">
                                Profile Number
                              </label>
                              <input
                                type="text"
                                value={experience.profileNumber || ""}
                                onChange={(e) =>
                                  handleExperienceChange(
                                    "profileNumber",
                                    e.target.value
                                  )
                                }
                                placeholder="Enter your profile number"
                                className="w-full p-2 border border-gray-300 rounded-lg text-sm"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "documents" && !canProceedToExperience) ||
                (currentStep === "experience" && !canSubmitApplication)
              }
              className={`flex w-full items-center justify-center rounded-full py-3 font-semibold transition-all ${
                (currentStep === "documents" && canProceedToExperience) ||
                (currentStep === "experience" && canSubmitApplication)
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              {currentStep === "documents"
                ? "Continue to Experience"
                : "Submit Application"}
            </button>
            {((currentStep === "documents" && !canProceedToExperience) ||
              (currentStep === "experience" && !canSubmitApplication)) && (
              <p className="text-xs text-[#797879] text-center mt-2">
                {currentStep === "documents"
                  ? "Please upload required documents (ID Document & Driver's License) to continue"
                  : "Please complete all required fields"}
              </p>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
