"use client";

import Image from "next/image";
import {
  ArrowLeft,
  Clock,
  FileText,
  Calendar,
  Eye,
  Mail,
  CreditCard,
  Key,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  She<PERSON><PERSON><PERSON>er,
  SheetTitle,
} from "@/components/ui/sheet";

interface Application {
  id: string;
  type: "vehicle_lease" | "business_opportunity" | "vehicle_listing";
  title: string;
  subtitle: string;
  status: "pending" | "in_review" | "approved" | "rejected";
  submittedDate: string;
  vehicleInfo?: {
    make: string;
    model: string;
    year: number;
    weeklyRate: number;
    image: string;
  };
}

interface StatusHistoryItem {
  id: string;
  status: string;
  title: string;
  description: string;
  timestamp: string;
  isCompleted: boolean;
  isCurrent: boolean;
  icon: React.ReactNode;
}

interface ApplicationStatusSheetProps {
  isOpen: boolean;
  onClose: () => void;
  application: Application | null;
}

export default function ApplicationStatusSheet({
  isOpen,
  onClose,
  application,
}: ApplicationStatusSheetProps) {
  if (!application) return null;

  // Mock status history - in a real app, this would come from an API
  const getStatusHistory = (app: Application): StatusHistoryItem[] => {
    const baseHistory: StatusHistoryItem[] = [
      {
        id: "1",
        status: "submitted",
        title: "Application Submitted",
        description: "Your application has been successfully submitted",
        timestamp: app.submittedDate,
        isCompleted: true,
        isCurrent: false,
        icon: <FileText size={16} className="text-white" />,
      },
      {
        id: "2",
        status: "review",
        title: "Under Review",
        description: "Our team is reviewing your application and documents",
        timestamp: "2024-01-16T09:00:00Z",
        isCompleted: app.status !== "pending",
        isCurrent: app.status === "in_review",
        icon: <Eye size={16} className="text-white" />,
      },
    ];

    if (app.type === "vehicle_lease") {
      baseHistory.push(
        {
          id: "3",
          status: "approval",
          title: "Application Decision",
          description:
            "You'll receive an email with the decision and next steps",
          timestamp: "",
          isCompleted: app.status === "approved" || app.status === "rejected",
          isCurrent: false,
          icon: <Mail size={16} className="text-white" />,
        },
        {
          id: "4",
          status: "payment",
          title: "Payment & Contract",
          description: "Complete payment and sign lease agreement",
          timestamp: "",
          isCompleted: false,
          isCurrent: false,
          icon: <CreditCard size={16} className="text-white" />,
        },
        {
          id: "5",
          status: "handover",
          title: "Vehicle Handover",
          description: "Schedule and complete vehicle handover process",
          timestamp: "",
          isCompleted: false,
          isCurrent: false,
          icon: <Key size={16} className="text-white" />,
        }
      );
    } else if (app.type === "vehicle_listing") {
      baseHistory.push(
        {
          id: "3",
          status: "inspection",
          title: "Vehicle Inspection",
          description: "Physical inspection of your vehicle will be scheduled",
          timestamp: "",
          isCompleted: false,
          isCurrent: app.status === "in_review",
          icon: <Eye size={16} className="text-white" />,
        },
        {
          id: "4",
          status: "approval",
          title: "Listing Approval",
          description: "Your vehicle listing will go live on the platform",
          timestamp: "",
          isCompleted: app.status === "approved",
          isCurrent: false,
          icon: <Mail size={16} className="text-white" />,
        },
        {
          id: "5",
          status: "matching",
          title: "Lessee Matching",
          description: "Start receiving applications from qualified drivers",
          timestamp: "",
          isCompleted: false,
          isCurrent: false,
          icon: <Key size={16} className="text-white" />,
        }
      );
    }

    return baseHistory;
  };

  const statusHistory = getStatusHistory(application);

  const formatDate = (dateString: string) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          text: "text-yellow-800",
        };
      case "in_review":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          text: "text-blue-800",
        };
      case "approved":
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          text: "text-green-800",
        };
      case "rejected":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          text: "text-red-800",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
        };
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onClose}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application Status
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Track your application progress
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Application Summary */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md overflow-hidden">
                {/* Vehicle Image for lease and listing applications */}
                {(application.type === "vehicle_lease" ||
                  application.type === "vehicle_listing") &&
                  application.vehicleInfo && (
                    <div className="h-32 bg-gray-100 relative overflow-hidden">
                      <Image
                        src={application.vehicleInfo.image}
                        alt={`${application.vehicleInfo.make} ${application.vehicleInfo.model}`}
                        fill
                        className="object-contain"
                      />
                      {application.type === "vehicle_listing" && (
                        <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                          <span className="text-xs font-medium text-white">
                            Listing
                          </span>
                        </div>
                      )}
                    </div>
                  )}

                <div className="p-4">
                  <div className="flex justify-between items-start gap-3 mb-2">
                    <h3 className="text-lg font-bold text-[#333333] flex-1 min-w-0">
                      {application.title}
                    </h3>
                    <span
                      className={`text-xs px-3 py-1 rounded-full font-medium whitespace-nowrap flex-shrink-0 ${getStatusColor(application.status).bg} ${getStatusColor(application.status).border} ${getStatusColor(application.status).text}`}
                      style={{ borderWidth: "1px" }}
                    >
                      {application.status === "pending" && "Pending Review"}
                      {application.status === "in_review" && "In Review"}
                      {application.status === "approved" && "Approved"}
                      {application.status === "rejected" && "Rejected"}
                    </span>
                  </div>
                  <p className="text-[#797879] mb-4">{application.subtitle}</p>

                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <span className="text-[#009639] font-bold text-xs">
                          #
                        </span>
                      </div>
                      <span className="text-[#797879]">Application ID: </span>
                      <span className="text-[#333333] font-medium">
                        {application.id}
                      </span>
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                        <Calendar size={10} className="text-[#009639]" />
                      </div>
                      <span className="text-[#797879]">Submitted: </span>
                      <span className="text-[#333333] font-medium">
                        {formatDate(application.submittedDate)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Status Timeline */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-4">
                  Status Timeline
                </h4>
                <div className="space-y-4">
                  {statusHistory.map((item, index) => (
                    <div key={item.id} className="flex items-start">
                      {/* Timeline Icon */}
                      <div className="flex flex-col items-center mr-4">
                        <div
                          className={`w-10 h-10 rounded-full flex items-center justify-center shadow-sm ${
                            item.isCompleted
                              ? "bg-[#009639]"
                              : item.isCurrent
                                ? "bg-[#3b82f6]"
                                : "bg-gray-300"
                          }`}
                        >
                          {item.icon}
                        </div>
                        {index < statusHistory.length - 1 && (
                          <div
                            className={`w-0.5 h-8 mt-2 ${
                              item.isCompleted ? "bg-[#009639]" : "bg-gray-200"
                            }`}
                          />
                        )}
                      </div>

                      {/* Timeline Content */}
                      <div className="flex-1 pb-4">
                        <div
                          className={`rounded-xl p-4 border ${
                            item.isCurrent
                              ? "bg-blue-50 border-blue-200"
                              : item.isCompleted
                                ? "bg-green-50 border-green-200"
                                : "bg-gray-50 border-gray-200"
                          }`}
                        >
                          <h5
                            className={`font-medium mb-1 ${
                              item.isCurrent
                                ? "text-blue-900"
                                : item.isCompleted
                                  ? "text-green-900"
                                  : "text-gray-700"
                            }`}
                          >
                            {item.title}
                          </h5>
                          <p
                            className={`text-sm mb-2 ${
                              item.isCurrent
                                ? "text-blue-700"
                                : item.isCompleted
                                  ? "text-green-700"
                                  : "text-gray-600"
                            }`}
                          >
                            {item.description}
                          </p>
                          {item.timestamp && (
                            <div className="flex items-center text-xs text-gray-500">
                              <Clock size={12} className="mr-1" />
                              {formatDate(item.timestamp)}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
