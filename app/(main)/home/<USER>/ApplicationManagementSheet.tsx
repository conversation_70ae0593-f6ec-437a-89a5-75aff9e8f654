"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Clock,
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  User,
  Car,
  FileText,
  Filter,
  Search,
  Users,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  Sheet<PERSON>eader,
  SheetTitle,
} from "@/components/ui/sheet";

interface Application {
  id: string;
  applicantName: string;
  applicantEmail: string;
  vehicleName: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  decisionDate?: string;
  decisionReason?: string;
  weeklyRate: number;
  experience?: {
    hasExperience: boolean;
    company?: string;
    duration?: string;
    profileNumber?: string;
  };
  documents?: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
  }[];
}

interface ApplicationManagementSheetProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onReviewApplication: (applicationId: string) => void;
}

export default function ApplicationManagementSheet({
  isOpen,
  onClose,
  onBack,
  onReviewApplication,
}: ApplicationManagementSheetProps) {
  const [currentTab, setCurrentTab] = useState<"active" | "history">("active");
  const [filterStatus, setFilterStatus] = useState<
    "all" | "pending" | "under_review" | "approved" | "rejected"
  >("all");
  const [searchQuery, setSearchQuery] = useState("");

  // Mock data - replace with actual data fetching
  const [applications] = useState<Application[]>([
    // Active applications (pending/under_review)
    {
      id: "1",
      applicantName: "John Doe",
      applicantEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2020",
      applicationDate: "2024-01-15",
      status: "pending",
      weeklyRate: 2700,
      experience: {
        hasExperience: true,
        company: "Uber",
        duration: "2-5-years",
        profileNumber: "78432",
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
        { name: "PrDP", uploaded: true, verified: false },
      ],
    },
    {
      id: "2",
      applicantName: "Sarah Smith",
      applicantEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2020",
      applicationDate: "2024-01-14",
      status: "under_review",
      weeklyRate: 2700,
      experience: {
        hasExperience: false,
      },
      documents: [
        { name: "ID Document", uploaded: true, verified: true },
        { name: "Driver's License", uploaded: true, verified: true },
      ],
    },
    // Historical applications (approved/rejected)
    {
      id: "3",
      applicantName: "Mike Johnson",
      applicantEmail: "<EMAIL>",
      vehicleName: "Honda Civic 2019",
      applicationDate: "2024-01-10",
      status: "approved",
      decisionDate: "2024-01-12",
      weeklyRate: 2500,
      experience: {
        hasExperience: true,
        company: "Bolt",
        duration: "1-2-years",
        profileNumber: "56789",
      },
    },
    {
      id: "4",
      applicantName: "Lisa Brown",
      applicantEmail: "<EMAIL>",
      vehicleName: "Honda Civic 2019",
      applicationDate: "2024-01-08",
      status: "rejected",
      decisionDate: "2024-01-09",
      decisionReason: "Insufficient e-hailing experience",
      weeklyRate: 2500,
      experience: {
        hasExperience: false,
      },
    },
    {
      id: "5",
      applicantName: "David Wilson",
      applicantEmail: "<EMAIL>",
      vehicleName: "Toyota Corolla 2020",
      applicationDate: "2024-01-05",
      status: "approved",
      decisionDate: "2024-01-06",
      weeklyRate: 2700,
      experience: {
        hasExperience: true,
        company: "Uber",
        duration: "5+-years",
        profileNumber: "92341",
      },
    },
  ]);

  const getStatusColor = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "under_review":
        return "bg-blue-100 text-blue-800";
      case "approved":
        return "bg-green-100 text-green-800";
      case "rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusIcon = (status: Application["status"]) => {
    switch (status) {
      case "pending":
        return <Clock size={12} />;
      case "under_review":
        return <Eye size={12} />;
      case "approved":
        return <CheckCircle size={12} />;
      case "rejected":
        return <XCircle size={12} />;
      default:
        return <Clock size={12} />;
    }
  };

  // Filter applications based on current tab
  const activeApplications = applications.filter(
    (app) => app.status === "pending" || app.status === "under_review"
  );

  const historicalApplications = applications.filter(
    (app) => app.status === "approved" || app.status === "rejected"
  );

  const currentApplications =
    currentTab === "active" ? activeApplications : historicalApplications;

  const filteredApplications = currentApplications.filter((app) => {
    const matchesFilter = filterStatus === "all" || app.status === filterStatus;
    const matchesSearch =
      app.applicantName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      app.vehicleName.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const getFilterCount = (filter: typeof filterStatus) => {
    if (filter === "all") return currentApplications.length;
    return currentApplications.filter((app) => app.status === filter).length;
  };

  const getTabCount = (tab: "active" | "history") => {
    return tab === "active"
      ? activeApplications.length
      : historicalApplications.length;
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Manage Applications
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Review applications and track history
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Tabs */}
          <div className="px-6 py-3 bg-white">
            <div className="flex border-b border-gray-200 mb-4">
              <button
                className={`flex-1 py-2 text-center text-sm font-medium ${
                  currentTab === "active"
                    ? "text-[#009639] border-b-2 border-[#009639]"
                    : "text-[#797879]"
                }`}
                onClick={() => setCurrentTab("active")}
              >
                Active ({getTabCount("active")})
              </button>
              <button
                className={`flex-1 py-2 text-center text-sm font-medium ${
                  currentTab === "history"
                    ? "text-[#009639] border-b-2 border-[#009639]"
                    : "text-[#797879]"
                }`}
                onClick={() => setCurrentTab("history")}
              >
                History ({getTabCount("history")})
              </button>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="px-6 py-4 bg-gray-50 border-b">
            <div className="flex items-center space-x-2 mb-3">
              <Search size={16} className="text-gray-400" />
              <input
                type="text"
                placeholder="Search applications..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639]"
              />
            </div>

            <div className="flex space-x-2 overflow-x-auto">
              {[
                { key: "all", label: "All" },
                ...(currentTab === "active"
                  ? [
                      { key: "pending", label: "Pending" },
                      { key: "under_review", label: "Review" },
                    ]
                  : [
                      { key: "approved", label: "Approved" },
                      { key: "rejected", label: "Rejected" },
                    ]),
              ].map((filter) => (
                <button
                  key={filter.key}
                  onClick={() =>
                    setFilterStatus(filter.key as typeof filterStatus)
                  }
                  className={`flex items-center space-x-1 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap transition-colors ${
                    filterStatus === filter.key
                      ? "bg-[#009639] text-white"
                      : "bg-white text-gray-600 border border-gray-200"
                  }`}
                >
                  <span>{filter.label}</span>
                  <span className="bg-white bg-opacity-20 px-1 rounded-full text-xs">
                    {getFilterCount(filter.key as typeof filterStatus)}
                  </span>
                </button>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {filteredApplications.length === 0 ? (
              <div className="text-center py-8">
                <Users size={48} className="mx-auto text-gray-300 mb-4" />
                <h3 className="text-lg font-semibold text-gray-600 mb-2">
                  No Applications Found
                </h3>
                <p className="text-sm text-gray-500">
                  {searchQuery
                    ? "Try adjusting your search"
                    : "No applications match the selected filter"}
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredApplications.map((application) => (
                  <div
                    key={application.id}
                    className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                  >
                    {/* Application Header */}
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold text-[#333333]">
                          {application.applicantName}
                        </h4>
                        <p className="text-xs text-[#797879]">
                          {application.vehicleName}
                        </p>
                      </div>
                      <div
                        className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}
                      >
                        {getStatusIcon(application.status)}
                        <span className="capitalize">
                          {application.status.replace("_", " ")}
                        </span>
                      </div>
                    </div>

                    {/* Application Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-xs text-[#797879]">
                        <Calendar size={12} className="mr-2" />
                        Applied:{" "}
                        {new Date(
                          application.applicationDate
                        ).toLocaleDateString()}
                      </div>
                      {application.decisionDate && (
                        <div className="flex items-center text-xs text-[#797879]">
                          <CheckCircle size={12} className="mr-2" />
                          Decision:{" "}
                          {new Date(
                            application.decisionDate
                          ).toLocaleDateString()}
                        </div>
                      )}
                      {application.documents && (
                        <div className="flex items-center text-xs text-[#797879]">
                          <FileText size={12} className="mr-2" />
                          Documents:{" "}
                          {
                            application.documents.filter((d) => d.uploaded)
                              .length
                          }
                          /{application.documents.length}
                        </div>
                      )}
                      {application.experience?.hasExperience && (
                        <div className="flex items-center text-xs text-[#797879]">
                          <CheckCircle
                            size={12}
                            className="mr-2 text-green-500"
                          />
                          Experience: {application.experience.company} (
                          {application.experience.duration})
                        </div>
                      )}
                    </div>

                    {/* Decision Reason */}
                    {application.decisionReason && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-2 mb-3">
                        <p className="text-xs text-red-800">
                          <strong>Reason:</strong> {application.decisionReason}
                        </p>
                      </div>
                    )}

                    {/* Actions */}
                    {currentTab === "active" ? (
                      <button
                        onClick={() => onReviewApplication(application.id)}
                        className="w-full bg-[#009639] text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors"
                      >
                        Review Application
                      </button>
                    ) : (
                      <div
                        className={`w-full py-2 px-4 rounded-full text-sm font-medium text-center ${
                          application.status === "approved"
                            ? "bg-green-50 text-green-700 border border-green-200"
                            : "bg-red-50 text-red-700 border border-red-200"
                        }`}
                      >
                        {application.status === "approved"
                          ? "Lease Active"
                          : "Application Rejected"}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Summary Footer */}
          <div className="border-t border-gray-200 p-4 bg-gray-50">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <p className="text-lg font-bold text-[#009639]">
                  {
                    applications.filter((item) => item.status === "approved")
                      .length
                  }
                </p>
                <p className="text-xs text-[#797879]">Approved</p>
              </div>
              <div>
                <p className="text-lg font-bold text-yellow-600">
                  {
                    applications.filter(
                      (item) =>
                        item.status === "pending" ||
                        item.status === "under_review"
                    ).length
                  }
                </p>
                <p className="text-xs text-[#797879]">Active</p>
              </div>
              <div>
                <p className="text-lg font-bold text-red-600">
                  {
                    applications.filter((item) => item.status === "rejected")
                      .length
                  }
                </p>
                <p className="text-xs text-[#797879]">Rejected</p>
              </div>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
