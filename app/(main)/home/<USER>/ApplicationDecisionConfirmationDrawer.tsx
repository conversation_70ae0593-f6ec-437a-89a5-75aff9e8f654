"use client";

import { useState } from "react";
import {
  CheckCircle,
  XCircle,
  Clock,
  MessageSquare,
  FileText,
  Calendar,
  Users,
  Car,
  ArrowRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface ApplicationDecisionConfirmationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  decision: "approved" | "rejected";
  applicantName: string;
  vehicleName: string;
  weeklyRate: number;
  reason?: string;
}

export default function ApplicationDecisionConfirmationDrawer({
  isOpen,
  onClose,
  decision,
  applicantName,
  vehicleName,
  weeklyRate,
  reason,
}: ApplicationDecisionConfirmationDrawerProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const approvedSteps = [
    {
      icon: <MessageSquare size={16} className="text-[#009639]" />,
      title: "Applicant Notified",
      description: `${applicantName} will receive an email confirmation of approval`,
      timeframe: "Immediate",
    },
    {
      icon: <FileText size={16} className="text-[#009639]" />,
      title: "Lease Agreement Preparation",
      description: "Legal documents will be prepared and sent for review",
      timeframe: "1-2 business days",
    },
    {
      icon: <Users size={16} className="text-[#009639]" />,
      title: "Meet & Greet Scheduled",
      description: "Coordinate vehicle handover and final documentation",
      timeframe: "3-5 business days",
    },
    {
      icon: <Car size={16} className="text-[#009639]" />,
      title: "Vehicle Handover",
      description: "Complete the lease agreement and hand over vehicle keys",
      timeframe: "Within 1 week",
    },
  ];

  const rejectedSteps = [
    {
      icon: <MessageSquare size={16} className="text-red-600" />,
      title: "Applicant Notified",
      description: `${applicantName} will receive an email with feedback`,
      timeframe: "Immediate",
    },
    {
      icon: <FileText size={16} className="text-red-600" />,
      title: "Application Archived",
      description: "Application moved to your history for future reference",
      timeframe: "Immediate",
    },
    {
      icon: <Users size={16} className="text-red-600" />,
      title: "Listing Remains Active",
      description: "Your vehicle listing continues to receive new applications",
      timeframe: "Ongoing",
    },
  ];

  const steps = decision === "approved" ? approvedSteps : rejectedSteps;

  const handleContinue = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className={`px-6 py-4 text-left ${
            decision === "approved" ? "bg-[#009639]" : "bg-red-600"
          }`}>
            <div className="flex items-center">
              <div className="mr-3 rounded-full p-2 bg-white bg-opacity-20">
                {decision === "approved" ? (
                  <CheckCircle size={24} className="text-white" />
                ) : (
                  <XCircle size={24} className="text-white" />
                )}
              </div>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Application {decision === "approved" ? "Approved" : "Rejected"}
                </SheetTitle>
                <SheetDescription className="text-sm text-white text-opacity-90">
                  {applicantName} • {vehicleName}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {/* Decision Summary */}
            <div className={`rounded-xl border p-4 mb-6 ${
              decision === "approved" 
                ? "bg-green-50 border-green-200" 
                : "bg-red-50 border-red-200"
            }`}>
              <div className="flex items-center mb-3">
                {decision === "approved" ? (
                  <CheckCircle size={20} className="text-green-600 mr-2" />
                ) : (
                  <XCircle size={20} className="text-red-600 mr-2" />
                )}
                <h3 className={`font-semibold ${
                  decision === "approved" ? "text-green-800" : "text-red-800"
                }`}>
                  Decision Confirmed
                </h3>
              </div>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Applicant:</span>
                  <span className="font-medium">{applicantName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Vehicle:</span>
                  <span className="font-medium">{vehicleName}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Weekly Rate:</span>
                  <span className="font-medium">R{weeklyRate.toLocaleString()}</span>
                </div>
                {reason && (
                  <div className="pt-2 border-t border-gray-200">
                    <span className="text-gray-600 text-xs">Reason:</span>
                    <p className="text-gray-800 text-sm mt-1">{reason}</p>
                  </div>
                )}
              </div>
            </div>

            {/* What Happens Next */}
            <div className="mb-6">
              <h3 className="font-semibold text-[#333333] mb-4 flex items-center">
                <Clock size={18} className="mr-2 text-[#009639]" />
                What Happens Next
              </h3>

              {/* Progress Indicator */}
              <div className="flex items-center mb-4">
                {steps.map((_, index) => (
                  <div key={index} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
                      index <= currentStep
                        ? decision === "approved" 
                          ? "bg-[#009639] text-white"
                          : "bg-red-600 text-white"
                        : "bg-gray-200 text-gray-600"
                    }`}>
                      {index + 1}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-8 h-px mx-2 ${
                        index < currentStep
                          ? decision === "approved"
                            ? "bg-[#009639]"
                            : "bg-red-600"
                          : "bg-gray-300"
                      }`} />
                    )}
                  </div>
                ))}
              </div>

              {/* Current Step Details */}
              <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                <div className="flex items-start">
                  <div className="mr-3 mt-1">
                    {steps[currentStep].icon}
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-[#333333] mb-1">
                      {steps[currentStep].title}
                    </h4>
                    <p className="text-sm text-[#797879] mb-2">
                      {steps[currentStep].description}
                    </p>
                    <div className="flex items-center text-xs text-[#009639]">
                      <Calendar size={12} className="mr-1" />
                      <span className="font-medium">{steps[currentStep].timeframe}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Information */}
            {decision === "approved" ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
                <p className="text-xs text-blue-800">
                  <strong>Important:</strong> You'll receive email updates at each step. 
                  Keep your contact information current in your profile settings.
                </p>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                <p className="text-xs text-yellow-800">
                  <strong>Tip:</strong> Your vehicle listing remains active. 
                  You can review and approve other qualified applicants at any time.
                </p>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="flex-1 py-3 px-4 border border-gray-200 text-gray-600 rounded-full font-medium hover:bg-gray-50 transition-colors"
              >
                Close
              </button>
              <button
                onClick={handleContinue}
                className={`flex-1 py-3 px-4 rounded-full font-medium transition-colors flex items-center justify-center ${
                  decision === "approved"
                    ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                    : "bg-red-600 text-white hover:bg-red-700"
                }`}
              >
                {currentStep < steps.length - 1 ? (
                  <>
                    Next Step
                    <ArrowRight size={16} className="ml-1" />
                  </>
                ) : (
                  "Done"
                )}
              </button>
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
