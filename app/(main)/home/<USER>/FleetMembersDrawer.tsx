"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Users,
  Mail,
  Plus,
  X,
  Percent,
  Crown,
  User,
  ChevronRight,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface FleetMember {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  isOwner: boolean;
  ownershipPercentage: number;
}

interface FleetMembersDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onComplete: (members: FleetMember[]) => void;
  isPoollyManaged: boolean;
  currentUserEmail: string;
  currentUserName: string;
  companyData?: any;
}

export default function FleetMembersDrawer({
  isOpen,
  onClose,
  onBack,
  onComplete,
  isPoollyManaged,
  currentUserEmail,
  currentUserName,
  companyData,
}: FleetMembersDrawerProps) {
  // Initialize with current user as creator
  const [members, setMembers] = useState<FleetMember[]>([
    {
      id: "creator",
      email: currentUserEmail,
      firstName: currentUserName.split(" ")[0] || "You",
      lastName: currentUserName.split(" ").slice(1).join(" ") || "",
      isOwner: true,
      ownershipPercentage: isPoollyManaged ? 100 : 100,
    },
  ]);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberFirstName, setNewMemberFirstName] = useState("");
  const [newMemberLastName, setNewMemberLastName] = useState("");
  const [newMemberIsOwner, setNewMemberIsOwner] = useState(false);
  const [newMemberOwnership, setNewMemberOwnership] = useState(10);

  const addMember = () => {
    if (
      !newMemberEmail.trim() ||
      !newMemberFirstName.trim() ||
      !newMemberLastName.trim()
    ) {
      alert("Please fill in all member details");
      return;
    }

    if (members.some((m) => m.email === newMemberEmail)) {
      alert("This email is already in the member list");
      return;
    }

    // Check ownership percentage limits for company fleets
    if (!isPoollyManaged && newMemberIsOwner) {
      const totalOwnership =
        members
          .filter((m) => m.isOwner)
          .reduce((sum, m) => sum + m.ownershipPercentage, 0) +
        newMemberOwnership;

      if (totalOwnership > 100) {
        alert("Total ownership cannot exceed 100%");
        return;
      }
    }

    const newMember: FleetMember = {
      id: Date.now().toString(),
      email: newMemberEmail.trim(),
      firstName: newMemberFirstName.trim(),
      lastName: newMemberLastName.trim(),
      isOwner: newMemberIsOwner,
      ownershipPercentage: newMemberIsOwner ? newMemberOwnership : 0,
    };

    setMembers([...members, newMember]);
    setNewMemberEmail("");
    setNewMemberFirstName("");
    setNewMemberLastName("");
    setNewMemberIsOwner(false);
    setNewMemberOwnership(10);
  };

  const removeMember = (id: string) => {
    // Don't allow removing the creator
    if (id === "creator") return;
    setMembers(members.filter((m) => m.id !== id));
  };

  const toggleMemberOwnership = (id: string) => {
    setMembers(
      members.map((member) =>
        member.id === id
          ? {
              ...member,
              isOwner: !member.isOwner,
              ownershipPercentage: !member.isOwner ? 10 : 0,
            }
          : member
      )
    );
  };

  const updateMemberOwnership = (id: string, percentage: number) => {
    setMembers(
      members.map((member) =>
        member.id === id
          ? { ...member, ownershipPercentage: percentage }
          : member
      )
    );
  };

  const handleComplete = () => {
    onComplete(members);
  };

  const totalOwnership = members
    .filter((m) => m.isOwner)
    .reduce((sum, m) => sum + m.ownershipPercentage, 0);

  const remainingOwnership = 100 - totalOwnership;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Fleet Members
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Add members to your fleet
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Add Member Form */}
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h4 className="font-semibold text-[#333333]">
                    Invite Members
                  </h4>
                  {!isPoollyManaged && (
                    <span className="text-sm text-[#797879]">
                      Remaining: {remainingOwnership}%
                    </span>
                  )}
                </div>

                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-3">
                    {/* Name Fields */}
                    <div className="grid grid-cols-2 gap-3">
                      <input
                        type="text"
                        value={newMemberFirstName}
                        onChange={(e) => setNewMemberFirstName(e.target.value)}
                        placeholder="First name"
                        className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                      />
                      <input
                        type="text"
                        value={newMemberLastName}
                        onChange={(e) => setNewMemberLastName(e.target.value)}
                        placeholder="Last name"
                        className="px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                      />
                    </div>

                    {/* Email */}
                    <input
                      type="email"
                      value={newMemberEmail}
                      onChange={(e) => setNewMemberEmail(e.target.value)}
                      placeholder="Email address"
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                    />

                    {/* Owner Toggle */}
                    {!isPoollyManaged && (
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center">
                          <Crown size={16} className="mr-2 text-[#009639]" />
                          <span className="text-sm font-medium text-[#333333]">
                            Is Owner
                          </span>
                        </div>
                        <button
                          onClick={() => setNewMemberIsOwner(!newMemberIsOwner)}
                          className={`w-12 h-6 rounded-full transition-colors ${
                            newMemberIsOwner ? "bg-[#009639]" : "bg-gray-300"
                          }`}
                        >
                          <div
                            className={`w-5 h-5 bg-white rounded-full transition-transform ${
                              newMemberIsOwner
                                ? "translate-x-6"
                                : "translate-x-0.5"
                            }`}
                          />
                        </button>
                      </div>
                    )}

                    {/* Ownership Percentage */}
                    {!isPoollyManaged && newMemberIsOwner && (
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-[#333333]">
                          Ownership:
                        </span>
                        <input
                          type="number"
                          min="1"
                          max={remainingOwnership}
                          value={newMemberOwnership}
                          onChange={(e) =>
                            setNewMemberOwnership(parseInt(e.target.value) || 0)
                          }
                          className="flex-1 px-3 py-2 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        />
                        <Percent size={16} className="text-[#797879]" />
                      </div>
                    )}

                    {/* Add Button */}
                    <button
                      onClick={addMember}
                      disabled={
                        !newMemberEmail.trim() ||
                        !newMemberFirstName.trim() ||
                        !newMemberLastName.trim() ||
                        (!isPoollyManaged &&
                          newMemberIsOwner &&
                          (newMemberOwnership <= 0 ||
                            newMemberOwnership > remainingOwnership))
                      }
                      className="w-full bg-[#009639] text-white py-2 rounded-full font-medium disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
                    >
                      <Plus size={16} className="mr-2" />
                      Add Member
                    </button>
                  </div>
                </div>
              </div>

              {/* Members List */}
              {members.length > 0 && (
                <div>
                  <h4 className="font-semibold text-[#333333] mb-3">
                    Members ({members.length})
                  </h4>
                  <div className="space-y-3">
                    {members.map((member) => (
                      <div
                        key={member.id}
                        className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-start flex-1">
                            <div className="w-6 h-6 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 shadow-sm">
                              {member.isOwner ? (
                                <Crown size={12} className="text-[#009639]" />
                              ) : (
                                <User size={12} className="text-[#009639]" />
                              )}
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-[#333333]">
                                {member.firstName} {member.lastName}
                                {member.id === "creator" && (
                                  <span className="text-xs text-[#009639] ml-2">
                                    (You)
                                  </span>
                                )}
                              </p>
                              <p className="text-sm text-[#797879]">
                                {member.email}
                              </p>
                              {!isPoollyManaged && member.isOwner && (
                                <p className="text-xs text-[#009639] font-medium">
                                  {member.ownershipPercentage}% ownership
                                </p>
                              )}
                            </div>
                          </div>
                          {member.id !== "creator" && (
                            <button
                              onClick={() => removeMember(member.id)}
                              className="text-red-500 hover:text-red-700 p-1"
                            >
                              <X size={16} />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Information */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-semibold text-green-900 mb-2">
                  {isPoollyManaged ? "Poolly Managed Fleet" : "Company Fleet"}
                </h5>
                <p className="text-sm text-green-800">
                  {isPoollyManaged
                    ? "Members will have equal access to fleet management features. Ownership structure is managed by Poolly."
                    : "Owners have full management rights and profit sharing based on ownership percentage. Non-owners have operational access only."}
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleComplete}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors flex items-center justify-center"
            >
              Create Fleet & Invite Members
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onBack}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Back
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
