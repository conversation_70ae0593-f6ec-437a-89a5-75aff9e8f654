"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import VehicleDiscoveryDrawer from "./_components/VehicleDiscoveryDrawer";
import ApplicationProcessDrawer from "./_components/ApplicationProcessDrawer";
import ApplicationSubmittedDrawer from "./_components/ApplicationSubmittedDrawer";
import ProfileSheet from "./_components/ProfileSheet";
import {
  Bell,
  Search,
  ChevronRight,
  Settings,
  Wallet,
  Car,
  Users,
  Activity,
  DollarSign,
  PlusCircle,
  ShoppingCart,
  Key,
  Handshake,
  Calendar,
} from "lucide-react";
import { IndividualRead } from "@/types/individuals";

export default function HomeScreen({
  individual,
  profilePic,
  externalId,
  email,
}: {
  individual: IndividualRead;
  profilePic: string | null;
  externalId: string;
  email: string;
}) {
  const router = useRouter();
  // State for notifications
  const [showNotifications, setShowNotifications] = useState(false);
  // State for intent-based toggle
  const [intentMode, setIntentMode] = useState<"EARN" | "NEED">("NEED");
  // State for vehicle discovery drawer
  const [showVehicleDrawer, setShowVehicleDrawer] = useState(false);
  // State for application process drawer
  const [showApplicationDrawer, setShowApplicationDrawer] = useState(false);
  // State for application submitted confirmation drawer
  const [showSubmittedDrawer, setShowSubmittedDrawer] = useState(false);
  // State for profile sheet
  const [showProfileSheet, setShowProfileSheet] = useState(false);
  const [openProfileToActivity, setOpenProfileToActivity] = useState(false);
  // State for selected vehicle
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);

  const recentActivities = [
    {
      id: 1,
      type: "booking",
      title: "You booked Toyota Hilux",
      time: "2 hours ago",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 2,
      type: "payment",
      title: "Monthly payment processed",
      time: "Yesterday",
      image: "/placeholder.svg?height=40&width=40",
    },
    {
      id: 3,
      type: "group",
      title: "John joined Family SUV group",
      time: "2 days ago",
      image: "/placeholder.svg?height=40&width=40",
    },
  ];

  const opportunities = [
    {
      id: 1,
      title: "Suzuki Desire",
      description: "Perfect for Uber & Bolt driving",
      earnings: "R2,700/week",
      category: "Available",
      image: "/images/cars/suzuki-dzire-2.png",
      type: "vehicle",
    },
    {
      id: 2,
      title: "Start Earning Today",
      description: "No upfront vehicle purchase required",
      earnings: "R7,500 initial fee",
      category: "Flexible",
      image: "/images/cars/driver-car-1.png?height=120&width=200",
      type: "benefit",
    },
    {
      id: 3,
      title: "Own After 36 Months",
      description: "Build equity while you drive and earn",
      earnings: "Full ownership",
      category: "Ownership",
      image: "/images/cars/driver-car-2.png?height=120&width=200",
      type: "benefit",
    },
  ];

  // Removed quickActions array as we're using inline components

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-[#009639] px-6 py-4 flex justify-between items-center border-b border-[#007A2F]">
        <div className="flex items-center">
          <div className="w-10 h-10 ride-avatar mr-3">
            <Image
              src={profilePic || "/placeholder.svg?height=40&width=40"}
              alt="Profile"
              width={40}
              height={40}
              className="object-cover"
            />
          </div>
          <div>
            <p className="text-white text-sm">Hi there,</p>
            <h2 className="text-white font-bold">
              {individual.first_name} {individual.last_name}
            </h2>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => console.log("Search clicked")}
          >
            <Search size={18} className="text-white" />
          </button>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm relative"
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <Bell size={18} className="text-white" />
            <span className="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full"></span>
          </button>
          <button
            className="bg-[#007A2F] p-2 rounded-full shadow-sm"
            onClick={() => setShowProfileSheet(true)}
          >
            <Settings size={18} className="text-white" />
          </button>
        </div>
      </div>

      {/* Promotional Card */}
      <div className="mx-4 my-4">
        <div className="relative rounded-2xl p-0 overflow-hidden h-72 shadow-lg">
          {/* Background with gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#00b347] via-[#009639] to-[#007A2F] opacity-90"></div>
          {/* Glass overlay */}
          <div className="absolute inset-0 bg-black/5 backdrop-filter backdrop-blur-[1px]"></div>
          {/* Content container */}
          <div className="absolute inset-0 p-6 border border-white/20 rounded-2xl">
            <div className="relative z-10">
              <h3 className="text-white font-semibold mb-1">
                Welcome to Poolly!
              </h3>
              <p className="text-white text-2xl font-bold mb-4">
                Vehicle opportunities made simple
              </p>
              <p className="text-white/80 mb-6 max-w-[70%]">
                Browse, co-own, earn, or manage - all in one app.
              </p>
              <button
                className="bg-[#FFD700] text-[#333333] font-medium px-6 py-3 rounded-full text-sm shadow-md hover:bg-[#e6c200] transition-colors"
                onClick={() => router.push("/opportunities")}
              >
                Vehicle Marketplace
              </button>
            </div>
            <div className="absolute right-0 bottom-0 transform translate-x-4 translate-y-2">
              <Image
                src="/car-enhanced-3.png"
                alt="Car"
                width={140}
                height={100}
                className="object-contain"
              />
            </div>
            {/* Decorative circles */}
            <div className="absolute top-4 right-20 w-16 h-16 rounded-full bg-white opacity-10"></div>
            <div className="absolute top-16 right-12 w-10 h-10 rounded-full bg-white opacity-10"></div>
            <div className="absolute bottom-20 left-10 w-20 h-20 rounded-full bg-white opacity-5"></div>
          </div>
        </div>
      </div>

      {/* Intent-Based Toggle */}
      <div className="px-4 mt-12">
        <h3 className="text-[#333333] font-semibold mb-4">
          What brings you here today?
        </h3>

        {/* Toggle Buttons */}
        <div className="flex bg-gray-100 rounded-full p-1 mb-6">
          <button
            className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
              intentMode === "NEED"
                ? "bg-[#009639] text-white shadow-md"
                : "text-[#797879] hover:text-[#333333]"
            }`}
            onClick={() => setIntentMode("NEED")}
          >
            I need a VEHICLE
          </button>
          <button
            className={`flex-1 py-3 px-4 rounded-full font-semibold text-sm transition-all duration-300 ${
              intentMode === "EARN"
                ? "bg-[#009639] text-white shadow-md"
                : "text-[#797879] hover:text-[#333333]"
            }`}
            onClick={() => setIntentMode("EARN")}
          >
            I want to EARN
          </button>
        </div>

        {/* Dynamic Content Based on Intent */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-100 p-4">
          {intentMode === "EARN" ? (
            <div>
              <div className="flex items-center mb-4">
                <DollarSign size={20} className="text-[#009639] mr-2" />
                <h4 className="text-[#333333] font-semibold">
                  Make money from your vehicle
                </h4>
              </div>
              <div className="grid grid-cols-1 gap-3">
                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => router.push("/list-vehicle")}
                >
                  <div className="bg-[#009639] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <PlusCircle size={18} className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      List My Vehicle
                    </p>
                    <p className="text-[#797879] text-xs">
                      Rent out your car to earn income
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>

                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => router.push("/list-vehicle")}
                >
                  <div className="bg-[#007A2F] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <Handshake size={18} className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      Sell Co-ownership
                    </p>
                    <p className="text-[#797879] text-xs">
                      Share ownership and costs
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>

                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => router.push("/opportunities?tab=business")}
                >
                  <div className="bg-[#FFD700] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <Calendar size={18} className="text-[#333333]" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      Rental Income
                    </p>
                    <p className="text-[#797879] text-xs">
                      View earning opportunities
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex items-center mb-4">
                <Car size={20} className="text-[#009639] mr-2" />
                <h4 className="text-[#333333] font-semibold">
                  Get the vehicle you need
                </h4>
              </div>
              <div className="grid grid-cols-1 gap-3">
                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => setShowVehicleDrawer(true)}
                >
                  <div className="bg-[#009639] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <ShoppingCart size={18} className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      Lease for e-hailing (Uber, Bolt, etc.)
                    </p>
                    <p className="text-[#797879] text-xs">
                      Find vehicles to lease for e-hailing
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>

                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => router.push("/opportunities")}
                >
                  <div className="bg-[#007A2F] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <Users size={18} className="text-white" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      Join Co-ownership
                    </p>
                    <p className="text-[#797879] text-xs">
                      Share a vehicle with others
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>

                <button
                  className="flex items-center p-3 bg-[#f8fff8] rounded-lg border border-[#e6ffe6] hover:bg-[#e6ffe6] transition-colors"
                  onClick={() => router.push("/opportunities?tab=business")}
                >
                  <div className="bg-[#FFD700] w-10 h-10 rounded-full flex items-center justify-center mr-3">
                    <Key size={18} className="text-[#333333]" />
                  </div>
                  <div className="flex-1 text-left">
                    <p className="text-[#333333] font-medium text-sm">
                      Rent a Vehicle
                    </p>
                    <p className="text-[#797879] text-xs">
                      Short-term vehicle rentals
                    </p>
                  </div>
                  <ChevronRight size={16} className="text-[#009639]" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Fleet Management */}
      <div className="px-4 mt-12">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[#333333] font-semibold">Fleet Management</h3>
            <p className="text-xs text-[#797879] mt-1">
              Manage multiple vehicles for your business
            </p>
          </div>
        </div>

        {/* Fleet Management Card */}
        <div className="ride-card overflow-hidden drop-shadow-md rounded-xl border border-gray-100">
          {/* Placeholder Image */}
          <div className="h-40 bg-[#f2f2f2] relative rounded-t-xl">
            <Image
              src="/images/cars/fleet-card-3.png?height=160&width=320"
              alt="Fleet Management Dashboard"
              fill
              className="object-cover"
            />
            <div className="absolute top-3 right-3 bg-[#009639] px-3 py-1 rounded-full">
              <span className="text-xs font-medium text-white">Fleet</span>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-4">
            {/* <h4 className="text-[#333333] font-semibold text-lg mb-2">
              Fleet Dashboard
            </h4> */}
            <p className="text-[#797879] text-sm mb-4 leading-relaxed">
              Complete fleet management solution for businesses
            </p>

            {/* USP Points */}
            <div className="grid grid-cols-2 gap-3 mb-4">
              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mr-2">
                  <Activity size={12} className="text-white" />
                </div>
                <span className="text-xs font-medium text-[#333333]">
                  Digital Handovers
                </span>
              </div>

              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#007A2F] rounded-full flex items-center justify-center mr-2">
                  <Settings size={12} className="text-white" />
                </div>
                <span className="text-xs font-medium text-[#333333]">
                  Smart Maintenance
                </span>
              </div>

              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#FFD700] rounded-full flex items-center justify-center mr-2">
                  <Car size={12} className="text-[#333333]" />
                </div>
                <span className="text-xs font-medium text-[#333333]">
                  Live Tracking
                </span>
              </div>

              <div className="flex items-center">
                <div className="w-6 h-6 bg-[#009639] rounded-full flex items-center justify-center mr-2">
                  <Wallet size={12} className="text-white" />
                </div>
                <span className="text-xs font-medium text-[#333333]">
                  Secure Documents
                </span>
              </div>
            </div>

            {/* CTA Button */}
            <button
              className="ride-primary-btn w-full text-sm py-3"
              onClick={() => router.push("/group-details/1")}
            >
              Get Started
            </button>
          </div>
        </div>
      </div>

      {/* Featured Opportunities */}
      <div className="px-4 mt-12">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h3 className="text-[#333333] font-semibold">
              Lease Vehicles for E-Hailing
            </h3>
            <p className="text-xs text-[#797879] mt-1">
              Weekly lease rates - Start driving today
            </p>
          </div>
          <button
            className="text-[#009639] text-sm font-medium flex items-center"
            onClick={() => router.push("/opportunities?tab=business")}
          >
            View All <ChevronRight size={14} />
          </button>
        </div>

        <div className="flex space-x-4 overflow-x-auto pb-4">
          {opportunities.map((opportunity) => (
            <div
              key={opportunity.id}
              className="ride-card min-w-[280px] overflow-hidden drop-shadow-md rounded-xl border border-gray-100"
            >
              <div className="h-32 bg-[#f2f2f2] relative rounded-t-xl">
                <Image
                  src={opportunity.image || "/placeholder.svg"}
                  alt={opportunity.title}
                  fill
                  className="object-cover"
                />
                <div className="absolute top-3 right-3 bg-[#009639] px-2 py-1 rounded-full">
                  <span className="text-xs font-medium text-white">
                    {opportunity.category}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h4 className="text-[#333333] font-semibold text-sm mb-1">
                  {opportunity.title}
                </h4>
                <p className="text-xs text-[#797879] mb-3 leading-relaxed">
                  {opportunity.description}
                </p>
                <div className="flex items-center justify-between mb-3">
                  <span className="text-xs text-[#797879]">
                    {opportunity.type === "vehicle"
                      ? "Weekly Rate"
                      : "Key Benefit"}
                  </span>
                  <span className="text-sm font-bold text-[#009639]">
                    {opportunity.earnings}
                  </span>
                </div>
                <button
                  className="ride-primary-btn w-full text-sm py-2"
                  onClick={() => setShowVehicleDrawer(true)}
                >
                  Start Lease Application
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="px-4 mt-12 mb-8">
        <h3 className="text-[#333333] font-semibold mb-4">Recent Activity</h3>
        <div className="p-1 bg-transparent">
          {recentActivities.length > 3 ? (
            recentActivities.map((activity) => (
              <div
                key={activity.id}
                className={`p-4 flex items-center bg-white rounded-lg drop-shadow-md mb-3 mx-1 border border-gray-100`}
              >
                <div className="w-10 h-10 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-md">
                  {activity.type === "booking" ? (
                    <Car size={20} className="text-[#009639]" />
                  ) : activity.type === "payment" ? (
                    <Wallet size={20} className="text-[#009639]" />
                  ) : activity.type === "group" ? (
                    <Users size={20} className="text-[#009639]" />
                  ) : (
                    <Activity size={20} className="text-[#009639]" />
                  )}
                </div>
                <div className="flex-1">
                  <h4 className="text-[#333333] text-sm font-medium">
                    {activity.title}
                  </h4>
                  <p className="text-xs text-[#797879]">{activity.time}</p>
                </div>
              </div>
            ))
          ) : (
            /* Empty State */
            <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-8 text-center">
              <div className="w-16 h-16 bg-[#f8f8f8] rounded-full flex items-center justify-center mx-auto mb-4">
                <Activity size={24} className="text-[#797879]" />
              </div>
              <h4 className="text-[#333333] font-medium mb-2">
                No Activity Yet
              </h4>
              <p className="text-[#797879] text-sm mb-4 leading-relaxed">
                Your recent bookings, payments, and group activities will appear
                here.
              </p>
              <button
                className="bg-[#009639] text-white px-6 py-2 rounded-full text-sm font-medium"
                onClick={() => router.push("/opportunities")}
              >
                Explore Opportunities
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Vehicle Discovery Drawer */}
      <VehicleDiscoveryDrawer
        isOpen={showVehicleDrawer}
        onClose={() => setShowVehicleDrawer(false)}
        onNext={(vehicle) => {
          setSelectedVehicle(vehicle);
          setShowVehicleDrawer(false);
          setShowApplicationDrawer(true);
        }}
      />

      {/* Application Process Drawer */}
      <ApplicationProcessDrawer
        isOpen={showApplicationDrawer}
        onClose={() => setShowApplicationDrawer(false)}
        onSubmit={() => {
          console.log("Application submitted for vehicle:", selectedVehicle);
          setShowApplicationDrawer(false);
          setShowSubmittedDrawer(true);
        }}
        selectedVehicle={selectedVehicle}
      />

      {/* Application Submitted Confirmation Drawer */}
      <ApplicationSubmittedDrawer
        isOpen={showSubmittedDrawer}
        onClose={() => setShowSubmittedDrawer(false)}
        selectedVehicle={selectedVehicle}
        onViewStatus={() => {
          setShowSubmittedDrawer(false);
          setOpenProfileToActivity(true);
          setShowProfileSheet(true);
        }}
      />

      {/* Profile Sheet */}
      <ProfileSheet
        isOpen={showProfileSheet}
        onClose={() => {
          setShowProfileSheet(false);
          setOpenProfileToActivity(false);
        }}
        individual={individual}
        profilePic={profilePic}
        externalId={externalId}
        email={email}
        openToActivityTab={openProfileToActivity}
      />
    </div>
  );
}
