"use client";

import { useState } from "react";
import {
  ArrowLeft,
  Building,
  FileText,
  MapPin,
  ChevronRight,
  Users,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface CompanyData {
  groupName: string;
  purpose: string;
  location: string;
  companyName: string;
  companyType: string;
  registrationNumber: string;
  registrationCountry: string;
}

interface CompanyRegistrationDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  onNext: (companyData: CompanyData) => void;
}

export default function CompanyRegistrationDrawer({
  isOpen,
  onClose,
  onBack,
  onNext,
}: CompanyRegistrationDrawerProps) {
  const [groupName, setGroupName] = useState("");
  const [purpose, setPurpose] = useState(
    "Fleet group to manage our fleet of vehicles"
  );
  const [location, setLocation] = useState("");
  const [companyName, setCompanyName] = useState("");
  const [companyType, setCompanyType] = useState("");
  const [registrationNumber, setRegistrationNumber] = useState("");
  const [registrationCountry, setRegistrationCountry] =
    useState("South Africa");

  const companyTypes = [
    { value: "private", label: "Private Company (Pty Ltd)" },
    { value: "public", label: "Public Company (Ltd)" },
    { value: "nonprofit", label: "Non-Profit Organization (NPO)" },
    { value: "sole_prop", label: "Sole Proprietorship" },
    { value: "partnership", label: "Partnership" },
    { value: "incorporated", label: "Incorporated (Inc)" },
    { value: "llc", label: "Limited Liability Company (LLC)" },
  ];

  const countries = [
    "South Africa",
    "United States",
    "United Kingdom",
    "Canada",
    "Australia",
    "Germany",
    "France",
    "Other",
  ];

  const handleContinue = () => {
    if (
      groupName.trim() &&
      purpose.trim() &&
      location.trim() &&
      companyName.trim() &&
      companyType &&
      registrationCountry
    ) {
      // Registration number is optional for sole proprietorships and partnerships
      if (
        companyType === "sole_prop" ||
        companyType === "partnership" ||
        registrationNumber.trim()
      ) {
        onNext({
          groupName: groupName.trim(),
          purpose: purpose.trim(),
          location: location.trim(),
          companyName: companyName.trim(),
          companyType,
          registrationNumber: registrationNumber.trim(),
          registrationCountry,
        });
      }
    }
  };

  const isFormValid = () => {
    const basicFieldsValid =
      groupName.trim() &&
      purpose.trim() &&
      location.trim() &&
      companyName.trim() &&
      companyType &&
      registrationCountry;
    const registrationValid =
      companyType === "sole_prop" ||
      companyType === "partnership" ||
      registrationNumber.trim();
    return basicFieldsValid && registrationValid;
  };

  const requiresRegistrationNumber =
    companyType && !["sole_prop", "partnership"].includes(companyType);

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Company Registration
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  Enter your existing company details
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-6">
              {/* Group Details */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Group Details
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-4">
                    {/* Group Name */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <Users size={16} className="mr-2 text-[#009639]" />
                        Group Name
                      </label>
                      <input
                        type="text"
                        value={groupName}
                        onChange={(e) => setGroupName(e.target.value)}
                        placeholder="Enter group name"
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        maxLength={100}
                      />
                    </div>

                    {/* Purpose */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <FileText size={16} className="mr-2 text-[#009639]" />
                        Purpose
                      </label>
                      <textarea
                        value={purpose}
                        onChange={(e) => setPurpose(e.target.value)}
                        placeholder="Describe the purpose of this fleet group"
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333] resize-none"
                        rows={3}
                        maxLength={500}
                      />
                    </div>

                    {/* Location */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <MapPin size={16} className="mr-2 text-[#009639]" />
                        Location
                      </label>
                      <input
                        type="text"
                        value={location}
                        onChange={(e) => setLocation(e.target.value)}
                        placeholder="Enter primary location (city, region)"
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        maxLength={100}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Company Information */}
              <div>
                <h4 className="font-semibold text-[#333333] mb-3">
                  Company Information
                </h4>
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="space-y-4">
                    {/* Company Name */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <Building size={16} className="mr-2 text-[#009639]" />
                        Company Name
                      </label>
                      <input
                        type="text"
                        value={companyName}
                        onChange={(e) => setCompanyName(e.target.value)}
                        placeholder="Enter your company name"
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        maxLength={100}
                      />
                    </div>

                    {/* Company Type */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <FileText size={16} className="mr-2 text-[#009639]" />
                        Company Type
                      </label>
                      <select
                        value={companyType}
                        onChange={(e) => setCompanyType(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                      >
                        <option value="">Select company type</option>
                        {companyTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Registration Number */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <FileText size={16} className="mr-2 text-[#009639]" />
                        Registration Number
                        {!requiresRegistrationNumber && (
                          <span className="text-xs text-[#797879] ml-1">
                            (Optional)
                          </span>
                        )}
                      </label>
                      <input
                        type="text"
                        value={registrationNumber}
                        onChange={(e) => setRegistrationNumber(e.target.value)}
                        placeholder={
                          requiresRegistrationNumber
                            ? "Enter registration number (e.g., 2023/123456/08)"
                            : "Enter registration number (optional)"
                        }
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                        maxLength={50}
                      />
                      {!requiresRegistrationNumber && (
                        <p className="text-xs text-[#797879] mt-1">
                          Registration number not required for{" "}
                          {companyType === "sole_prop"
                            ? "sole proprietorships"
                            : "partnerships"}
                        </p>
                      )}
                    </div>

                    {/* Registration Country */}
                    <div>
                      <label className="flex items-center text-sm font-medium text-[#333333] mb-2">
                        <MapPin size={16} className="mr-2 text-[#009639]" />
                        Registration Country
                      </label>
                      <select
                        value={registrationCountry}
                        onChange={(e) => setRegistrationCountry(e.target.value)}
                        className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:border-[#009639] text-[#333333]"
                      >
                        {countries.map((country) => (
                          <option key={country} value={country}>
                            {country}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              {/* Information Notice */}
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h5 className="font-semibold text-green-900 mb-2">
                  Why do we need this information?
                </h5>
                <p className="text-sm text-green-800">
                  Company registration details help us verify your business
                  entity and ensure compliance with local regulations for fleet
                  management operations.
                </p>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4 space-y-3">
            <button
              onClick={handleContinue}
              disabled={!isFormValid()}
              className="w-full bg-[#009639] text-white py-3 rounded-full font-semibold transition-colors disabled:bg-gray-300 disabled:cursor-not-allowed flex items-center justify-center"
            >
              Continue to Members
              <ChevronRight size={16} className="ml-1" />
            </button>
            <button
              onClick={onBack}
              className="w-full bg-gray-100 text-gray-700 hover:bg-gray-200 py-3 rounded-full font-medium transition-colors"
            >
              Back
            </button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
