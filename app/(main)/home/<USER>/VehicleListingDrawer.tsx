"use client";

import { useState } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  Upload,
  X,
  FileText,
  Camera,
  CheckCircle,
  Clock,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface VehicleData {
  make: string;
  model: string;
  year: string;
  color: string;
  mileage: string;
  condition: string;
  images: File[];
  documents: DocumentUpload[];
}

interface DocumentUpload {
  name: string;
  uploaded: boolean;
  required: boolean;
  file?: File;
}

interface VehicleListingDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onNext: (vehicleData: VehicleData) => void;
}

export default function VehicleListingDrawer({
  isOpen,
  onClose,
  onNext,
}: VehicleListingDrawerProps) {
  const [currentStep, setCurrentStep] = useState<
    "info" | "details" | "photos" | "documents"
  >("info");
  const [showDetails, setShowDetails] = useState(false);
  const [vehicleData, setVehicleData] = useState<VehicleData>({
    make: "",
    model: "",
    year: "",
    color: "",
    mileage: "",
    condition: "excellent",
    images: [],
    documents: [],
  });

  const [documents, setDocuments] = useState<DocumentUpload[]>([
    { name: "Vehicle Registration Document", uploaded: false, required: true },
    { name: "Vehicle License Document", uploaded: false, required: true },
    { name: "DEKRA Inspection Certificate", uploaded: false, required: true },
    { name: "Insurance Certificate", uploaded: false, required: false },
    { name: "Service History", uploaded: false, required: false },
  ]);

  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const handleVehicleDataChange = (field: keyof VehicleData, value: string) => {
    setVehicleData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    if (files.length > 0) {
      const newImages = [...vehicleData.images, ...files].slice(0, 6); // Max 6 images
      const newPreviews = files.map((file) => URL.createObjectURL(file));

      setVehicleData((prev) => ({ ...prev, images: newImages }));
      setImagePreviews((prev) => [...prev, ...newPreviews].slice(0, 6));
    }
  };

  const removeImage = (index: number) => {
    const newImages = vehicleData.images.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);

    // Revoke the URL to prevent memory leaks
    URL.revokeObjectURL(imagePreviews[index]);

    setVehicleData((prev) => ({ ...prev, images: newImages }));
    setImagePreviews(newPreviews);
  };

  const handleDocumentUpload = (index: number, file: File) => {
    const updatedDocuments = [...documents];
    updatedDocuments[index] = {
      ...updatedDocuments[index],
      uploaded: true,
      file,
    };
    setDocuments(updatedDocuments);
  };

  const canProceedFromDetails = () => {
    return (
      vehicleData.make &&
      vehicleData.model &&
      vehicleData.year &&
      vehicleData.color &&
      vehicleData.mileage
    );
  };

  const canProceedFromPhotos = () => {
    return vehicleData.images.length >= 3; // Minimum 3 photos
  };

  const canProceedFromDocuments = () => {
    const requiredDocs = documents.filter((doc) => doc.required);
    return requiredDocs.every((doc) => doc.uploaded);
  };

  const handleViewDetails = () => {
    setShowDetails(true);
  };

  const handleNext = () => {
    if (currentStep === "info" && showDetails) {
      setCurrentStep("details");
    } else if (currentStep === "details" && canProceedFromDetails()) {
      setCurrentStep("photos");
    } else if (currentStep === "photos" && canProceedFromPhotos()) {
      setCurrentStep("documents");
    } else if (currentStep === "documents" && canProceedFromDocuments()) {
      const finalData = { ...vehicleData, documents };
      onNext(finalData);
    }
  };

  const handleBack = () => {
    if (currentStep === "details") {
      setCurrentStep("info");
    } else if (currentStep === "photos") {
      setCurrentStep("details");
    } else if (currentStep === "documents") {
      setCurrentStep("photos");
    } else {
      onClose();
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case "info":
        return "List Your Vehicle";
      case "details":
        return "Vehicle Details";
      case "photos":
        return "Vehicle Photos";
      case "documents":
        return "Required Documents";
      default:
        return "List Your Vehicle";
    }
  };

  const getStepDescription = () => {
    switch (currentStep) {
      case "info":
        return "Learn about the listing process";
      case "details":
        return "Tell us about your vehicle";
      case "photos":
        return "Upload clear photos of your vehicle";
      case "documents":
        return "Upload required documentation";
      default:
        return "Start listing your vehicle";
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={handleBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  {getStepTitle()}
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {getStepDescription()}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator - Hide on info step */}
          {currentStep !== "info" && (
            <div className="px-6 py-3 bg-gray-50 border-b">
              <div className="flex items-center justify-between text-xs text-gray-600">
                <span
                  className={
                    currentStep === "details"
                      ? "text-[#009639] font-medium"
                      : ""
                  }
                >
                  Details
                </span>
                <span
                  className={
                    currentStep === "photos" ? "text-[#009639] font-medium" : ""
                  }
                >
                  Photos
                </span>
                <span
                  className={
                    currentStep === "documents"
                      ? "text-[#009639] font-medium"
                      : ""
                  }
                >
                  Documents
                </span>
              </div>
              <div className="mt-2 h-1 bg-gray-200 rounded-full">
                <div
                  className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                  style={{
                    width:
                      currentStep === "details"
                        ? "33%"
                        : currentStep === "photos"
                          ? "66%"
                          : "100%",
                  }}
                />
              </div>
            </div>
          )}

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {currentStep === "info" && (
              <div className="space-y-8">
                {/* Initial Information Card */}
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <div className="text-center">
                    <div className="w-16 h-16 bg-[#e6ffe6] rounded-full flex items-center justify-center mx-auto mb-4">
                      <Camera size={32} className="text-[#009639]" />
                    </div>
                    <h3 className="text-lg font-bold text-[#333333] mb-2">
                      List Your Vehicle
                    </h3>
                    <p className="text-sm text-[#797879] mb-4">
                      Turn your vehicle into a source of income by listing it
                      for lease-to-own arrangements with qualified drivers.
                    </p>

                    {/* Key Benefits */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center text-sm text-[#797879]">
                        <CheckCircle
                          size={16}
                          className="text-[#009639] mr-2"
                        />
                        <span>Earn weekly rental income</span>
                      </div>
                      <div className="flex items-center text-sm text-[#797879]">
                        <CheckCircle
                          size={16}
                          className="text-[#009639] mr-2"
                        />
                        <span>Path to vehicle ownership for lessees</span>
                      </div>
                      <div className="flex items-center text-sm text-[#797879]">
                        <CheckCircle
                          size={16}
                          className="text-[#009639] mr-2"
                        />
                        <span>Platform handles matching & verification</span>
                      </div>
                    </div>

                    {/* View Details Button */}
                    <div className="mt-4">
                      <button
                        onClick={handleViewDetails}
                        className="w-full bg-[#009639] text-white py-2 px-4 rounded-full text-sm font-medium hover:bg-[#007A2F] transition-colors"
                      >
                        View Listing Details
                      </button>
                    </div>
                  </div>
                </div>

                {/* Detail Cards - Show only when details are requested */}
                {showDetails && (
                  <div className="space-y-6 mt-6 animate-in slide-in-from-bottom-4 duration-500">
                    {/* Process Overview Card */}
                    <div>
                      <div className="flex items-center mb-3">
                        <Clock size={18} className="mr-2 text-[#009639]" />
                        <h4 className="font-semibold text-[#333333]">
                          Listing Process Overview
                        </h4>
                      </div>
                      <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                        <div className="space-y-3">
                          {[
                            "Upload vehicle details & photos",
                            "Submit required documents",
                            "Set financial terms",
                            "Define lessee requirements",
                            "Receive platform approval",
                            "Start receiving applications",
                          ].map((step, index) => (
                            <div
                              key={index}
                              className="flex items-center text-sm text-[#797879]"
                            >
                              <div className="w-6 h-6 bg-[#009639] text-white rounded-full flex items-center justify-center mr-3 text-xs font-medium">
                                {index + 1}
                              </div>
                              {step}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Required Documents Card */}
                    <div>
                      <div className="flex items-center mb-3">
                        <FileText size={18} className="mr-2 text-[#009639]" />
                        <h4 className="font-semibold text-[#333333]">
                          Required Documents
                        </h4>
                      </div>
                      <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                        <div className="space-y-2">
                          {[
                            "Vehicle Registration Document",
                            "Vehicle License Document",
                            "DEKRA Inspection Certificate",
                            "Insurance Certificate (optional)",
                            "Service History (optional)",
                          ].map((doc, index) => (
                            <div
                              key={index}
                              className="flex items-center text-sm text-[#797879]"
                            >
                              <div className="w-2 h-2 bg-[#009639] rounded-full mr-3"></div>
                              {doc}
                            </div>
                          ))}
                        </div>
                        <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-3">
                          <p className="text-xs text-blue-800">
                            <strong>Note:</strong> You'll upload these documents
                            in step 3. Make sure all documents are clear and
                            legible (PDF, JPG, PNG formats accepted).
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {currentStep === "details" && (
              <div className="space-y-6">
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Vehicle Information
                  </h4>
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Make
                        </label>
                        <input
                          type="text"
                          value={vehicleData.make}
                          onChange={(e) =>
                            handleVehicleDataChange("make", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. Toyota"
                        />
                      </div>
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Model
                        </label>
                        <input
                          type="text"
                          value={vehicleData.model}
                          onChange={(e) =>
                            handleVehicleDataChange("model", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. Corolla"
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Year
                        </label>
                        <input
                          type="number"
                          value={vehicleData.year}
                          onChange={(e) =>
                            handleVehicleDataChange("year", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="2020"
                          min="2000"
                          max="2024"
                        />
                      </div>
                      <div>
                        <label className="text-[#797879] text-xs mb-1 block">
                          Color
                        </label>
                        <input
                          type="text"
                          value={vehicleData.color}
                          onChange={(e) =>
                            handleVehicleDataChange("color", e.target.value)
                          }
                          className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                          placeholder="e.g. White"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Mileage (km)
                      </label>
                      <input
                        type="number"
                        value={vehicleData.mileage}
                        onChange={(e) =>
                          handleVehicleDataChange("mileage", e.target.value)
                        }
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                        placeholder="e.g. 50000"
                      />
                    </div>

                    <div>
                      <label className="text-[#797879] text-xs mb-1 block">
                        Condition
                      </label>
                      <select
                        value={vehicleData.condition}
                        onChange={(e) =>
                          handleVehicleDataChange("condition", e.target.value)
                        }
                        className="w-full px-3 py-2 rounded-lg border border-gray-200 focus:outline-none focus:border-[#009639] text-sm"
                      >
                        <option value="excellent">Excellent</option>
                        <option value="good">Good</option>
                        <option value="fair">Fair</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "photos" && (
              <div className="space-y-6">
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Vehicle Photos
                  </h4>
                  <p className="text-sm text-[#797879] mb-4">
                    Upload at least 3 clear photos of your vehicle (maximum 6)
                  </p>

                  {/* Photo Upload Grid */}
                  <div className="grid grid-cols-2 gap-3 mb-4">
                    {imagePreviews.map((preview, index) => (
                      <div
                        key={index}
                        className="relative aspect-square rounded-lg overflow-hidden border border-gray-200"
                      >
                        <Image
                          src={preview}
                          alt={`Vehicle photo ${index + 1}`}
                          fill
                          className="object-cover"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <X size={12} />
                        </button>
                      </div>
                    ))}

                    {/* Add Photo Button */}
                    {imagePreviews.length < 6 && (
                      <label className="aspect-square border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-[#009639] hover:bg-green-50 transition-colors">
                        <Camera size={24} className="text-gray-400 mb-2" />
                        <span className="text-xs text-gray-500 text-center">
                          Add Photo
                        </span>
                        <input
                          type="file"
                          accept="image/*"
                          multiple
                          onChange={handleImageUpload}
                          className="hidden"
                        />
                      </label>
                    )}
                  </div>

                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                    <p className="text-xs text-blue-800">
                      <strong>Photo Tips:</strong> Include exterior shots from
                      all angles, interior photos, and any special features.
                      Good lighting helps showcase your vehicle better.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "documents" && (
              <div className="space-y-6">
                <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                  <h4 className="font-semibold text-[#333333] mb-4">
                    Required Documents
                  </h4>
                  <p className="text-sm text-[#797879] mb-4">
                    Upload the following documents to verify your vehicle
                  </p>

                  <div className="space-y-3">
                    {documents.map((doc, index) => (
                      <div
                        key={index}
                        className="border border-gray-200 rounded-lg p-3"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center">
                            <FileText
                              size={16}
                              className="text-[#009639] mr-2"
                            />
                            <span className="text-sm font-medium text-[#333333]">
                              {doc.name}
                            </span>
                            {doc.required && (
                              <span className="text-xs text-red-500 ml-2">
                                *Required
                              </span>
                            )}
                          </div>
                          {doc.uploaded && (
                            <CheckCircle size={16} className="text-green-500" />
                          )}
                        </div>

                        {!doc.uploaded ? (
                          <label className="flex items-center justify-center w-full py-2 px-3 border border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-[#009639] hover:bg-green-50 transition-colors">
                            <Upload size={14} className="mr-2 text-gray-400" />
                            <span className="text-xs text-gray-600">
                              Upload Document
                            </span>
                            <input
                              type="file"
                              accept=".pdf,.jpg,.jpeg,.png"
                              onChange={(e) => {
                                const file = e.target.files?.[0];
                                if (file) {
                                  handleDocumentUpload(index, file);
                                }
                              }}
                              className="hidden"
                            />
                          </label>
                        ) : (
                          <div className="flex items-center text-xs text-green-600">
                            <CheckCircle size={12} className="mr-1" />
                            Document uploaded successfully
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                    <p className="text-xs text-yellow-800">
                      <strong>Note:</strong> All documents must be clear and
                      legible. Accepted formats: PDF, JPG, PNG (max 5MB per
                      file).
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <button
              onClick={handleNext}
              disabled={
                (currentStep === "info" && !showDetails) ||
                (currentStep === "details" && !canProceedFromDetails()) ||
                (currentStep === "photos" && !canProceedFromPhotos()) ||
                (currentStep === "documents" && !canProceedFromDocuments())
              }
              className={`w-full py-3 rounded-full font-semibold transition-all ${
                (currentStep === "info" && showDetails) ||
                (currentStep === "details" && canProceedFromDetails()) ||
                (currentStep === "photos" && canProceedFromPhotos()) ||
                (currentStep === "documents" && canProceedFromDocuments())
                  ? "bg-[#009639] text-white hover:bg-[#007A2F]"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              }`}
            >
              {currentStep === "documents" ? "Continue to Terms" : "Continue"}
            </button>
            {currentStep === "info" && !showDetails && (
              <p className="text-xs text-[#797879] text-center mt-2">
                Please view listing details to continue
              </p>
            )}
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
