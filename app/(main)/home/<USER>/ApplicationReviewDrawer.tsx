"use client";

import { useState } from "react";
import {
  ArrowLeft,
  User,
  FileText,
  CheckCircle,
  XCircle,
  Download,
  Eye,
  Clock,
  Star,
  Car,
  DollarSign,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";

interface ApplicationData {
  id: string;
  applicantName: string;
  applicantEmail: string;
  applicantPhone: string;
  applicationDate: string;
  status: "pending" | "under_review" | "approved" | "rejected";
  vehicleId: string;
  vehicleName: string;
  experience: {
    hasExperience: boolean;
    company?: string;
    duration?: string;
    workType?: string;
    profileNumber?: string;
  };
  documents: {
    name: string;
    uploaded: boolean;
    verified?: boolean;
    fileUrl?: string;
  }[];
  weeklyRate: number;
}

interface ApplicationReviewDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  onBack: () => void;
  applicationId: string | null;
  onDecision: (
    applicationId: string,
    decision: "approved" | "rejected",
    reason?: string
  ) => void;
}

export default function ApplicationReviewDrawer({
  isOpen,
  onClose,
  onBack,
  applicationId,
  onDecision,
}: ApplicationReviewDrawerProps) {
  const [currentStep, setCurrentStep] = useState<
    "overview" | "documents" | "decision"
  >("overview");
  const [decisionReason, setDecisionReason] = useState("");

  // Mock data - replace with actual data fetching based on applicationId
  const application: ApplicationData = {
    id: "1",
    applicantName: "John Doe",
    applicantEmail: "<EMAIL>",
    applicantPhone: "+27 82 123 4567",
    applicationDate: "2024-01-15",
    status: "pending",
    vehicleId: "v1",
    vehicleName: "Toyota Corolla 2020",
    experience: {
      hasExperience: true,
      company: "Uber",
      duration: "2-5-years",
      workType: "full-time",
      profileNumber: "78432",
    },
    documents: [
      {
        name: "ID Document",
        uploaded: true,
        verified: true,
        fileUrl: "/docs/id.pdf",
      },
      {
        name: "Driver's License",
        uploaded: true,
        verified: true,
        fileUrl: "/docs/license.pdf",
      },
      {
        name: "PrDP",
        uploaded: true,
        verified: false,
        fileUrl: "/docs/prdp.pdf",
      },
      {
        name: "Proof of Address",
        uploaded: true,
        verified: true,
        fileUrl: "/docs/address.pdf",
      },
    ],
    weeklyRate: 2700,
  };

  const handleDecision = (decision: "approved" | "rejected") => {
    onDecision(application.id, decision, decisionReason);
    onClose();
  };

  const getDocumentStatus = (doc: ApplicationData["documents"][0]) => {
    if (!doc.uploaded)
      return {
        color: "text-gray-400",
        icon: <XCircle size={16} />,
        text: "Not uploaded",
      };
    if (doc.verified === false)
      return {
        color: "text-yellow-600",
        icon: <Clock size={16} />,
        text: "Pending verification",
      };
    if (doc.verified === true)
      return {
        color: "text-green-600",
        icon: <CheckCircle size={16} />,
        text: "Verified",
      };
    return {
      color: "text-blue-600",
      icon: <Eye size={16} />,
      text: "Under review",
    };
  };

  if (!applicationId) return null;

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent side="right" className="w-full max-w-md p-0">
        <div className="flex h-full flex-col">
          {/* Header */}
          <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
            <div className="flex items-center">
              <button
                onClick={onBack}
                className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
              >
                <ArrowLeft size={24} />
              </button>
              <div>
                <SheetTitle className="text-xl font-bold text-white">
                  Review Application
                </SheetTitle>
                <SheetDescription className="text-sm text-green-100">
                  {application.applicantName} • {application.vehicleName}
                </SheetDescription>
              </div>
            </div>
          </SheetHeader>

          {/* Progress Indicator */}
          <div className="px-6 py-3 bg-gray-50 border-b">
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span
                className={
                  currentStep === "overview" ? "text-[#009639] font-medium" : ""
                }
              >
                Overview
              </span>
              <span
                className={
                  currentStep === "documents"
                    ? "text-[#009639] font-medium"
                    : ""
                }
              >
                Documents
              </span>
              <span
                className={
                  currentStep === "decision" ? "text-[#009639] font-medium" : ""
                }
              >
                Decision
              </span>
            </div>
            <div className="mt-2 h-1 bg-gray-200 rounded-full">
              <div
                className="h-1 bg-[#009639] rounded-full transition-all duration-300"
                style={{
                  width:
                    currentStep === "overview"
                      ? "33%"
                      : currentStep === "documents"
                        ? "66%"
                        : "100%",
                }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-4">
            {currentStep === "overview" && (
              <div className="space-y-6">
                {/* Applicant Profile */}
                <div>
                  <div className="flex items-center mb-3">
                    <User size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Applicant Profile
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-3">
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          {application.applicantName}
                        </p>
                        <p className="text-xs text-[#797879]">
                          {application.applicantEmail}
                        </p>
                        <p className="text-xs text-[#797879]">
                          {application.applicantPhone}
                        </p>
                      </div>
                      <div className="pt-2 border-t border-gray-100">
                        <p className="text-xs text-[#797879] mb-1">
                          Applied on
                        </p>
                        <p className="text-sm text-[#333333]">
                          {new Date(
                            application.applicationDate
                          ).toLocaleDateString("en-US", {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Vehicle & Terms */}
                <div>
                  <div className="flex items-center mb-3">
                    <Car size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Vehicle & Terms
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-[#333333]">
                          {application.vehicleName}
                        </p>
                        <p className="text-xs text-[#797879]">
                          Weekly lease rate
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-lg font-bold text-[#009639]">
                          R{application.weeklyRate.toLocaleString()}
                        </p>
                        <p className="text-xs text-[#797879]">per week</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Experience */}
                <div>
                  <div className="flex items-center mb-3">
                    <Star size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      E-hailing Experience
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    {application.experience.hasExperience ? (
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <CheckCircle
                            size={16}
                            className="text-green-500 mr-2"
                          />
                          <span className="text-sm text-[#333333]">
                            Has e-hailing experience
                          </span>
                        </div>
                        <div className="grid grid-cols-2 gap-3 text-xs">
                          <div>
                            <p className="text-[#797879] mb-1">Company</p>
                            <p className="text-[#333333] font-medium">
                              {application.experience.company}
                            </p>
                          </div>
                          <div>
                            <p className="text-[#797879] mb-1">Duration</p>
                            <p className="text-[#333333] font-medium">
                              {application.experience.duration?.replace(
                                "-",
                                " - "
                              )}
                            </p>
                          </div>
                          <div>
                            <p className="text-[#797879] mb-1">Work Type</p>
                            <p className="text-[#333333] font-medium capitalize">
                              {application.experience.workType}
                            </p>
                          </div>
                          <div>
                            <p className="text-[#797879] mb-1">
                              Profile Number
                            </p>
                            <p className="text-[#333333] font-medium">
                              {application.experience.profileNumber}
                            </p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <XCircle size={16} className="text-yellow-500 mr-2" />
                        <span className="text-sm text-[#333333]">
                          No e-hailing experience
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {currentStep === "documents" && (
              <div className="space-y-6">
                <div>
                  <div className="flex items-center mb-3">
                    <FileText size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Document Verification
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      {application.documents.map((doc, index) => {
                        const status = getDocumentStatus(doc);
                        return (
                          <div
                            key={index}
                            className="border border-gray-200 rounded-lg p-3"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-[#333333]">
                                {doc.name}
                              </span>
                              <div
                                className={`flex items-center space-x-1 ${status.color}`}
                              >
                                {status.icon}
                                <span className="text-xs">{status.text}</span>
                              </div>
                            </div>
                            {doc.uploaded && (
                              <div className="flex space-x-2">
                                <button className="flex items-center space-x-1 px-2 py-1 bg-blue-50 text-blue-600 rounded text-xs hover:bg-blue-100 transition-colors">
                                  <Eye size={12} />
                                  <span>View</span>
                                </button>
                                <button className="flex items-center space-x-1 px-2 py-1 bg-gray-50 text-gray-600 rounded text-xs hover:bg-gray-100 transition-colors">
                                  <Download size={12} />
                                  <span>Download</span>
                                </button>
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {currentStep === "decision" && (
              <div className="space-y-6">
                <div>
                  <div className="flex items-center mb-3">
                    <CheckCircle size={18} className="mr-2 text-[#009639]" />
                    <h4 className="font-semibold text-[#333333]">
                      Application Decision
                    </h4>
                  </div>
                  <div className="rounded-xl bg-white border border-gray-100 drop-shadow-md p-4">
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium text-[#333333] mb-2 block">
                          Decision Reason (Optional)
                        </label>
                        <textarea
                          value={decisionReason}
                          onChange={(e) => setDecisionReason(e.target.value)}
                          placeholder="Add a note about your decision..."
                          className="w-full p-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:border-[#009639] resize-none"
                          rows={3}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <button
                          onClick={() => handleDecision("rejected")}
                          className="flex items-center justify-center space-x-2 py-3 px-4 bg-red-500 text-white rounded-full font-medium hover:bg-red-600 transition-colors"
                        >
                          <XCircle size={16} />
                          <span>Reject</span>
                        </button>
                        <button
                          onClick={() => handleDecision("approved")}
                          className="flex items-center justify-center space-x-2 py-3 px-4 bg-[#009639] text-white rounded-full font-medium hover:bg-[#007A2F] transition-colors"
                        >
                          <CheckCircle size={16} />
                          <span>Approve</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex space-x-3">
              {currentStep !== "overview" && (
                <button
                  onClick={() =>
                    setCurrentStep(
                      currentStep === "decision" ? "documents" : "overview"
                    )
                  }
                  className="flex-1 py-3 px-4 border border-gray-200 text-gray-600 rounded-full font-medium hover:bg-gray-50 transition-colors"
                >
                  Back
                </button>
              )}
              {currentStep !== "decision" && (
                <button
                  onClick={() =>
                    setCurrentStep(
                      currentStep === "overview" ? "documents" : "decision"
                    )
                  }
                  className="flex-1 py-3 px-4 bg-[#009639] text-white rounded-full font-medium hover:bg-[#007A2F] transition-colors"
                >
                  {currentStep === "overview"
                    ? "Review Documents"
                    : "Make Decision"}
                </button>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
}
