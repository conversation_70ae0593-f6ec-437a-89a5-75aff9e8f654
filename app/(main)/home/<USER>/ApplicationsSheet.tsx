"use client";

import { useState } from "react";
import Image from "next/image";
import {
  ArrowLeft,
  FileText,
  Calendar,
  Clock,
  ChevronRight,
  Car,
} from "lucide-react";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { IndividualRead } from "@/types/individuals";
import ApplicationStatusSheet from "./ApplicationStatusSheet";

interface Application {
  id: string;
  type: "vehicle_lease" | "business_opportunity";
  title: string;
  subtitle: string;
  status: "pending" | "in_review" | "approved" | "rejected";
  submittedDate: string;
  vehicleInfo?: {
    make: string;
    model: string;
    year: number;
    weeklyRate: number;
    image: string;
  };
}

interface ApplicationsSheetProps {
  isOpen: boolean;
  onClose: () => void;
  individual: IndividualRead;
}

export default function ApplicationsSheet({
  isOpen,
  onClose,
  individual,
}: ApplicationsSheetProps) {
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [showStatusSheet, setShowStatusSheet] = useState(false);

  // Mock data - in a real app, this would come from an API
  const applications: Application[] = [
    {
      id: "APP001234",
      type: "vehicle_lease",
      title: "Vehicle Lease Application",
      subtitle: "Suzuki Dzire 2023",
      status: "pending",
      submittedDate: "2024-01-15T10:30:00Z",
      vehicleInfo: {
        make: "Suzuki",
        model: "Dzire",
        year: 2023,
        weeklyRate: 2700,
        image: "/images/cars/suzuki-dzire.webp",
      },
    },
    {
      id: "APP001235",
      type: "business_opportunity",
      title: "Corporate Fleet Partnership",
      subtitle: "TechCorp Inc.",
      status: "in_review",
      submittedDate: "2024-01-10T14:20:00Z",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          text: "text-yellow-800",
          color: "#ffd700",
        };
      case "in_review":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          text: "text-blue-800",
          color: "#3b82f6",
        };
      case "approved":
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          text: "text-green-800",
          color: "#10b981",
        };
      case "rejected":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          text: "text-red-800",
          color: "#ef4444",
        };
      default:
        return {
          bg: "bg-gray-50",
          border: "border-gray-200",
          text: "text-gray-800",
          color: "#6b7280",
        };
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-ZA", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "pending":
        return "Pending Review";
      case "in_review":
        return "In Review";
      case "approved":
        return "Approved";
      case "rejected":
        return "Rejected";
      default:
        return "Unknown";
    }
  };

  const handleApplicationClick = (application: Application) => {
    setSelectedApplication(application);
    setShowStatusSheet(true);
  };

  return (
    <>
      <Sheet open={isOpen} onOpenChange={onClose}>
        <SheetContent side="right" className="w-full max-w-md p-0">
          <div className="flex h-full flex-col">
            {/* Header */}
            <SheetHeader className="bg-[#009639] px-6 py-4 text-left">
              <div className="flex items-center">
                <button
                  onClick={onClose}
                  className="mr-3 rounded-full p-1 text-white hover:bg-[#007A2F] transition-colors"
                >
                  <ArrowLeft size={24} />
                </button>
                <div>
                  <SheetTitle className="text-xl font-bold text-white">
                    Applications
                  </SheetTitle>
                  <SheetDescription className="text-sm text-green-100">
                    Track your application status
                  </SheetDescription>
                </div>
              </div>
            </SheetHeader>

            {/* Content */}
            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-4">
                {applications.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      No Applications Yet
                    </h3>
                    <p className="text-gray-500">
                      Your applications will appear here once you submit them.
                    </p>
                  </div>
                ) : (
                  applications.map((application) => {
                    const statusStyle = getStatusColor(application.status);
                    return (
                      <div
                        key={application.id}
                        className="bg-white rounded-xl border border-gray-100 drop-shadow-md overflow-hidden cursor-pointer hover:shadow-lg transition-shadow"
                        onClick={() => handleApplicationClick(application)}
                      >
                        {/* Vehicle Image for lease applications */}
                        {application.type === "vehicle_lease" && application.vehicleInfo && (
                          <div className="h-32 bg-gray-100 relative overflow-hidden">
                            <Image
                              src={application.vehicleInfo.image}
                              alt={`${application.vehicleInfo.make} ${application.vehicleInfo.model}`}
                              fill
                              className="object-contain"
                            />
                          </div>
                        )}

                        <div className="p-4">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1">
                              <h3 className="text-lg font-bold text-[#333333] mb-1">
                                {application.title}
                              </h3>
                              <p className="text-[#797879] text-sm">
                                {application.subtitle}
                              </p>
                            </div>
                            <span
                              className={`text-xs px-3 py-1 rounded-full font-medium ${statusStyle.bg} ${statusStyle.border} ${statusStyle.text}`}
                              style={{ borderWidth: "1px" }}
                            >
                              {getStatusText(application.status)}
                            </span>
                          </div>

                          <div className="space-y-2 mb-4">
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <span className="text-[#009639] font-bold text-xs">#</span>
                              </div>
                              <span className="text-[#797879]">ID: </span>
                              <span className="text-[#333333] font-medium">
                                {application.id}
                              </span>
                            </div>
                            <div className="flex items-center text-sm">
                              <div className="w-5 h-5 bg-[#e6ffe6] rounded-full flex items-center justify-center mr-2">
                                <Calendar size={10} className="text-[#009639]" />
                              </div>
                              <span className="text-[#797879]">Submitted: </span>
                              <span className="text-[#333333] font-medium">
                                {formatDate(application.submittedDate)}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center justify-between">
                            <div className="flex items-center text-sm text-[#009639]">
                              <Clock size={16} className="mr-1" />
                              <span>View Status Timeline</span>
                            </div>
                            <ChevronRight size={20} className="text-[#797879]" />
                          </div>
                        </div>
                      </div>
                    );
                  })
                )}
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>

      {/* Application Status Sheet */}
      <ApplicationStatusSheet
        isOpen={showStatusSheet}
        onClose={() => setShowStatusSheet(false)}
        application={selectedApplication}
      />
    </>
  );
}
